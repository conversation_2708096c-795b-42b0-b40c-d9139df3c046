# Betgames FE Workspace

<a alt="BG logo" href="https://betgames.tv" target="_blank" rel="noreferrer"><img src="https://www.betgames.tv/api/uploads/BG_Logo_White_Horizontal_Lock_Up_dcca475d41.png" height="45"></a>
<a alt="Nx logo" href="https://nx.dev" target="_blank" rel="noreferrer"><img src="https://raw.githubusercontent.com/nrwl/nx/master/images/nx-logo.png" style="margin-left: 15px; height: 45px"></a>

**This workspace has been generated by [Nx, Smart Monorepos · Fast CI.](https://nx.dev)**

### Aplications included (`apps/`)

-   `iframe-shell` - iframe host app. (Currently also includes the remotes which should be moved to separate directories)
-   `iframe-server` - The server for serving the app.
-   `iframe-loader` - Betgames loader.

### Remote Aplications included (`apps/iframe-remotes/`)

-   `roulette` - Roulette game.

### Libraries included (`libs/`)

-   `shared` - includes all the shared logic between applications and libraries. this is the top level shared library which is not dependant on anything else.
-   `components` - includes all the shared components between host and remote applications.

## Prerequisite
- Install [Node.js](https://nodejs.org/en/download/)
- Install [pnpm]
  - corepack enable
  - corepack prepare pnpm@8.6.5 --activate
- Run `make dev-certs`

## Start iframe app

### Production Mode (docker) iframe app
1.  Make sure `configs/iframe/config.development.json` is configured properly with port 80.
2.  Run `make run` to build and start the docker container.
3.  Open your browser and navigate to <https://webiframe.betgames.test/>.
4.  After pulling new code, switching branches - run `make restart`;

### Production Mode (local) iframe app
1. Change the port in `configs/iframe/config.development.json`
2. run `make reset`. wait for a few seconds
3. run `make run-prod`

## Development Mode iframe app

### Changing default port
For changing the port on development mode, you should do it per project. In `iframe-shell` and if requires, the projects in the `/iframe-remotes` directory, you can change the port in the `serve` command of `project.json` file. **Remember, range *8081-8099* is reserved for the remote game.**

```json
"serve": {
    "dependsOn": ["iframe-loader:build"],
    "executor": "@nx/react:module-federation-dev-server",
    "cache": true,
    "options": {
        "buildTarget": "iframe-shell:build:development",
        "port": 8080, // Change this to your desired port - e.g. 8070
        "hmr": true,
        "devRemotes": ["roulette"] // If you don't want the roulette game to be served on development mode when you run `make run-dev`, remove it from here.
    }
},
```

### Dev remotes

By default, by running the `make run-dev` command, all remote games will be served in their production mode (from cached build). If you desire to serve a specific remote game in development mode, you can do so by these two approaches:

1. Adding the remote game name in the `devRemotes` array in the `serve` command of the `iframe-shell` project. in this case each time you run `make run-dev` those games in the array will be served in development mode.

```json
"serve": {
    //...Other configurations
    "options": {
        //...Other configurations
        "devRemotes": ["roulette"] // By running `make run-dev` roulette will be served in development mode.
    }
    //...Other configurations
},
```

2. By adding the games to the `dr` variable in the `make run-dev` command.

```bash
    make run-dev dr=all # All dev remotes - based on the name of remotes in the iframe-remotes directory
    make run-dev dr=skyward,giza,roulette,starzle # 4 dev remotes
    make run-dev dr=skyward # 1 dev remote
    make run-dev # no dev remote
```
## Deployment for iframe app
1. Make sure PR is ready, all steps are completed
2. Write in #releases slack channel that you are preparing a release [ticket_number]
3. Merge PR
4. Use jenkins https://jenkins01.betgames.tv/view/iframe-webapp/
5. Build first the project with merged master
6. Deploy to non-prod envs like staging, audit, integrations, demo
7. After QA confirmation deploy to prod envs

## Start skyward-retail-screen app

### Production Mode (local) skyward-retail-screen app
1.  Make sure `apps/retail-skyward-screen/.env` is configured properly
2.  Run `make rss-run-prod` to build and start the docker container.
3.  Open your browser and navigate to <http://localhost:[PORT]/screen>. PORT for nestjs application in .env

### Development Mode skyward-retail-screen app
1.  Make sure `apps/retail-skyward-screen/.env` is configured properly
2.  Run `make rss-run-dev` to build and start the app
3.  Open your browser and navigate to <http://localhost:[PORT]/screen>. PORT for nestjs application in .env

## Deployment for skyward-retail-screen app
1. Make sure PR is ready, all steps are completed
2. Write in #releases slack channel that you are preparing a release [ticket_number]
3. Merge PR
4. Use jenkins https://jenkins01.betgames.tv/view/skyward-retail-screen/
5. Build first the project with merged master
6. Deploy to non-prod envs like staging
7. After QA confirmation deploy to prod envs

If you happen to use Nx plugins, you can leverage code generators that might come with it.

Run `nx list` to get a list of available plugins and whether they have generators. Then run `nx list <plugin-name>` to see what generators are available.

Learn more about [Nx generators on the docs](https://nx.dev/features/generate-code).

## Running tasks

To execute tasks with Nx use the following syntax:

```shell
nx <target> <project> <...options>

# nx serve iframe-shell
# nx serve iframe-shell --configuration production

# nx build iframe-server
```

To execute tasks in parallel use the following syntax:

```bash
nx run-many -t <target>
nx run-many -t <target> -p <...projects>

# nx run-many -t lint
# nx run-many -t lint -p iframe-server iframe-shell
```

Targets can be defined in the `package.json` or `projects.json`. Learn more [in the docs](https://nx.dev/features/run-tasks).

## Want better Editor Integration?

Have a look at the [Nx Console extensions](https://nx.dev/nx-console). It provides autocomplete support, a UI for exploring and running tasks & generators, and more! Available for VSCode, IntelliJ and comes with a LSP for Vim users.

## Explore the Project Graph

Run `nx graph` to show the graph of the workspace.
It will show tasks that you can run with Nx.

-   [Learn more about Exploring the Project Graph](https://nx.dev/core-features/explore-graph)

## TO DO

-   [x] Applying Micro-Frontend architecture
    -   [ ] Move each game into separate applications (`apps/remotes/`) and utilize them as served statically.
-   [ ] Create Generators for games
    -   Leverage the process of creating new games and applying micro-frontend configurations by using Nx generators.
-   [x] Stylelint is added
-   [x] Applying CI/CD configurations.
-   [x] Adding MakeFile for general commands.
-   [ ] Applying Nx smart cache.
-   [ ] utilizing `@bg-components` as single entry point for all shared components.
-   [ ] Refactor usage `import { ... } from '@bg-component'` inside `libs/shared/*`, probably move to `@bg-component`.


## Known issues

- Usage of `import { ... } from '@bg-shared'` inside `libs/shared/*` creates circular dependency injection, instead use full path `import { ... } from '@bg-shared/utils/X.ts'`
- Shared library files `libs/shared/*` shouldn't use `import { ... } from '@bg-component'` because it could create circular dependency injection when imported component uses shared library, should be refactored.

