window.resizeTo = (width, height) => {
    Object.assign(window, {
        innerWidth: width,
        innerHeight: height,
        outerWidth: width,
        outerHeight: height,
    }).dispatchEvent(new window.Event('resize'));
};

window.scroll = jest.fn();

window.AudioContext = jest.fn();

window.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
}));
