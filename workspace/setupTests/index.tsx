import 'reflect-metadata';
import '@testing-library/jest-dom';
import MatchMediaMock from 'jest-matchmedia-mock';
import { configure } from '@testing-library/react';
import { setDefaultOptions } from 'date-fns';
import { enGB } from 'date-fns/locale';
import { TextEncoder } from 'util';
import './setupWindowFnMocks';
import './setupProviders';

global.TextEncoder = TextEncoder;

jest.spyOn(console, 'error').mockImplementation(() => {});
jest.spyOn(console, 'warn').mockImplementation((...args) => {
    if (typeof args[0] === 'string' && args[0].startsWith('Store with key')) {
        return;
    }
    console.warn(...args);
});

// eslint-disable-next-line no-new
new MatchMediaMock();

setDefaultOptions({
    locale: enGB,
});

configure({
    testIdAttribute: 'data-qa',
});

jest.mock('howler');

jest.mock('screenfull', () => ({
    isFullscreen: false,
    isEnabled: false,
}));

jest.mock('@bg-shared/infrastructure/WebSockets/WebSockets.repository');
jest.mock('@bg-shared/business/Gamification/services/CaptainUpAPI/CaptainUp.api');
jest.mock('@bg-shared/infrastructure/HttpClient/HttpClient.repository');

jest.mock('@bg-shared/business/FavoriteOptions/FavoriteOptions.api');
jest.mock('@bg-shared/business/LastResults/LastResults.api');
jest.mock('@bg-shared/business/Promotions/Promotions.api');
jest.mock('@bg-shared/business/Reports/Reports.api');
jest.mock('@bg-shared/business/Games/GamesService');
jest.mock('@bg-shared/business/Betting/BettingService');
jest.mock('@bg-shared/business/GamesStatus/GamesStatus.service');
