import { taxes } from '@betgames/bg-tools';
import { sessionStore } from '@bg-shared/business/Session';
import { partnerSettingsStore } from '@bg-shared/business/PartnerSettings';
import { t } from '@bg-shared/business/Translate';
import { authStore } from '@bg-shared/business/Auth';
import { balanceStore } from '@bg-shared/business/Balance';
import { promotions } from '@bg-shared/business/Promotions';
import { IResponseInitial } from '@bg-shared/infrastructure/Initial/interfaces';
import { ISession } from '@bg-shared/business/Session/interfaces';

import sessionFixture from '@bg-shared/business/Session/__mocks__/fixture/session.json';
import initialFixture from '@bg-shared/infrastructure/Initial/__mocks__/fixture/initial.fixture.json';
import translationsFixture from '@bg-shared/business/Translate/__mocks__/fixture/translations.fixture.json';

t.update(translationsFixture.translations as any);
partnerSettingsStore.update(initialFixture as IResponseInitial, 'en');
sessionStore.update(sessionFixture as Partial<ISession>);
authStore.update(true);
taxes.configure(initialFixture.taxes, initialFixture.currency);
balanceStore.updateBalanceStore({ balance: '9,984.84€' });
promotions.store.init();
