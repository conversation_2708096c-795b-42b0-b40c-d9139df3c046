import { composePlugins, withNx } from '@nx/webpack';
import { withReact } from '@nx/react';
import { withModuleFederation } from '@nx/react/module-federation';
import { ICreateComposedPlugins, INxWebpackExecutionContext } from './interfaces/IWebpackConfig';

import baseConfigHandler from './configs/webpack.base';
import devConfigHandler from './configs/webpack.dev';
import prodConfigHandler from './configs/webpack.prod';

const envs = {
    isDevelopment: process.env.NODE_ENV === 'development',
};

export const createComposedPlugins: ICreateComposedPlugins = ({
    nxPluginOptions = {},
    moduleFederationConfig,
    workspacesOptions,
}) =>
    composePlugins(
        withNx(nxPluginOptions),
        withReact({
            stylePreprocessorOptions: {
                // to compile scss files in other libraries
                includePaths: ['libs/shared/src', 'libs/components/src'],
            },
            // should be false so we can import SVGs as ReactComponent
            svgr: false,
        }),

        // Fix for the build issue with the module federation plugin: { dts: false }
        // https://github.com/nrwl/nx/issues/27198#issuecomment-2275420582
        // https://module-federation.io/configure/dts.html
        withModuleFederation(moduleFederationConfig, { dts: false }),

        (webpackConfig, { options, context }: INxWebpackExecutionContext) => {
            if (!webpackConfig.module) {
                return webpackConfig;
            }

            let modifiedWebpackConfig = baseConfigHandler(
                webpackConfig,
                { options, context },
                envs,
                workspacesOptions,
            );

            if (envs.isDevelopment) {
                modifiedWebpackConfig = devConfigHandler(webpackConfig, { options, context }, envs);
            } else {
                modifiedWebpackConfig = prodConfigHandler(
                    webpackConfig,
                    { options, context },
                    envs,
                );
            }

            return modifiedWebpackConfig;
        },
    );
