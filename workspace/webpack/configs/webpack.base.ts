/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */

import path from 'path';
import { RuleSetRule } from 'webpack';

import HtmlWebpackPlugin from 'html-webpack-plugin';
import CopyWebpackPlugin from 'copy-webpack-plugin';
import postcssRTLCSS from 'postcss-rtlcss';

import matomoScript from 'apps/iframe-server/src/middlewares/matomo/matomoScript';
import features from 'apps/iframe-server/src/middlewares/config/features';

import { IWebpackConfig } from '../interfaces/IWebpackConfig';
import { ProjectType } from '../enums/ProjectType';

const config: IWebpackConfig = (
    webpackConfig,
    { options },
    { isDevelopment },
    workspaceOptions,
) => {
    if (!webpackConfig.module || !webpackConfig.output?.path) {
        return webpackConfig;
    }

    const {
        type,
        root,
        outputPath: baseOutputPath,
        sourceRoot: baseSourceRoot,
        projectRoot: baseProjectRoot,
    } = options;

    const isRemote = type === ProjectType.REMOTE;

    const outputPath = path.join(root, baseOutputPath);
    const outputFileName = isDevelopment ? '[name].js' : '[name].[contenthash].es5.js';
    const prodOutputPath = '';
    const sourceRoot = path.join(root, baseSourceRoot);
    const projectRoot = path.join(root, baseProjectRoot);

    webpackConfig.output = {
        ...webpackConfig.output,
        path: `${outputPath}${isDevelopment ? '' : `${prodOutputPath}/assets`}`,
        filename: outputFileName,
        chunkFilename: outputFileName,
        publicPath: isDevelopment ? 'auto' : `${prodOutputPath}/assets/`,
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let loadersUsePostcss: any[];
    let loadersUsePostcssIndex: number;
    let loadersUsePostcssCopy;

    // modifications on webpack rules
    (webpackConfig.module.rules as RuleSetRule[]).forEach((rule) => {
        // changing filenames to prevent webpack conflict error and have a cleaner build directory
        if ((rule as RuleSetRule).test.toString().includes('png')) {
            rule.generator.filename = 'assets/images/[name][ext]';
        }

        if (rule.test.toString().includes('woff')) {
            rule.generator.filename = 'assets/fonts/[name][ext]';
        }

        if (rule.test.toString().includes('svg')) {
            rule.generator.filename = 'assets/images/[name][ext]';
            rule.resourceQuery = /url/; // *.svg?url - default handler only handles SVGs with query
        }

        // modifying style loader to work with our development environment
        if (rule.test.toString().includes('.css')) {
            (rule.oneOf as RuleSetRule[]).forEach((loaders, index) => {
                if (loaders.test.toString().includes('/\\.module\\.(scss|sass)$/')) {
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    (loaders.use as any[]).forEach((loader) => {
                        if (loader.loader.includes('/css-loader/')) {
                            loader.options.modules = {
                                ...loader.options.modules,
                                localIdentName: (() => {
                                    if (isDevelopment) {
                                        return '[name]__[local]__[hash:base64:5]';
                                    }

                                    if (options.isTesting) {
                                        return '[path][name]__[local]';
                                    }

                                    return '[hash:base64]';
                                })(),
                                exportLocalsConvention: 'camelCase',
                            };
                            // opt-out checking public file urls in stylesheets
                            loader.options.url = {
                                filter: (url: string) => !url.includes('/static/'),
                            };
                        } else if (loader.loader.includes('/postcss-loader/')) {
                            loader.options.postcssOptions = loader.options.postcssOptions();
                            loader.options.postcssOptions.plugins.push('postcss-preset-env');

                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            loadersUsePostcss = loaders.use as any[];
                            loadersUsePostcssIndex = index + 1;
                            loadersUsePostcssCopy = {
                                loader: loader.loader,
                                options: {
                                    implementation: loader.options.implementation,
                                    postcssOptions: {
                                        plugins: [postcssRTLCSS()],
                                    },
                                },
                            };
                        }
                    });
                }
            });
        }
    });

    // handling SVGs via ReactComponent
    webpackConfig.module.rules.push({
        test: /\.svg$/,
        issuer: /\.(tsx|ts)$/,
        resourceQuery: { not: [/url/] }, // exclude react component if *.svg?url
        use: [
            {
                loader: 'babel-loader',
                options: {
                    presets: ['@babel/preset-react'],
                },
            },
            {
                loader: '@svgr/webpack',
                options: {
                    babel: false,
                    exportType: 'named',
                    svgoConfig: {
                        plugins: [
                            {
                                name: 'prefixIds',
                                params: {
                                    prefixClassNames: false,
                                },
                            },
                        ],
                    },
                },
            },
        ],
    });

    // handling .mp4 files
    webpackConfig.module.rules.push({
        test: /\.mp4$/,
        type: 'asset/resource',
        generator: {
            filename: 'assets/video/[name].[contenthash][ext]',
        },
    });

    // TODO: temp issue: https://github.com/Hacker0x01/react-datepicker/issues/5416
    webpackConfig.ignoreWarnings = [
        /Failed to parse source map/,
    ];

    // modify default mini-css-extract-plugin
    const miniCssWebpackPluginIndex = webpackConfig.plugins.findIndex(
        (plugin) => plugin.constructor?.name === 'MiniCssExtractPlugin',
    );
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const miniCssExtractPlugin = webpackConfig.plugins[miniCssWebpackPluginIndex] as any;
    miniCssExtractPlugin.options = {
        ...miniCssExtractPlugin.options,
        filename: isDevelopment ? '[name].css' : '[name].[contenthash].css',
        chunkFilename: isDevelopment ? '[id].css' : '[id].[contenthash].css',
        ignoreOrder: true,
    };

    // add a copy of postcss-loader rule which contains postcss-rtlcss plugin to css be parsed one plugin after another
    loadersUsePostcss.splice(loadersUsePostcssIndex, 0, loadersUsePostcssCopy);

    if (isRemote) {
        return webpackConfig;
    }
    // ---- End of remote configuration ----

    // add plugins for host applications

    webpackConfig.plugins.push(
        new HtmlWebpackPlugin({
            template: path.join(sourceRoot, '/templates/index.ejs'),
            inject: false,
            filename: isDevelopment ? 'index.html' : '../views/index.ejs',
            templateParameters: (() => {
                let devConfig;

                if (isDevelopment) {
                    devConfig = require(
                        path.resolve(root, 'configs/iframe/config.development.json'),
                    );
                }

                return {
                    environment: isDevelopment ? 'development' : '<%= environment %>',
                    apiUrl: isDevelopment ? devConfig.urls.api.default : '<%= apiUrl %>',
                    wsUrl: isDevelopment ? devConfig.urls.ws.default : '<%= wsUrl %>',
                    sentryScript: !isDevelopment ? '<%- sentryScript %>' : '',
                    matomoScript: isDevelopment
                        ? matomoScript({
                              buildType: 'legacy',
                              siteID: devConfig.matomoSiteId,
                              abTests: features.TEST?.abTests ?? [],
                              url: '',
                          })
                        : '<%- matomoScript %>',
                    featuresConfig: isDevelopment
                        ? JSON.stringify(features.TEST)
                        : '<%- featuresConfig %>',
                    loadLogo: !!features.TEST?.brandedConfig?.logos,
                    version: JSON.stringify(
                        require(path.resolve(projectRoot, workspaceOptions.versionFile)).version,
                    ),
                    hotjarScript: !isDevelopment ? '<%- hotjarScript %>' : '',
                    sentryRelease: process.env.SENTRY_RELEASE,
                    buildType: 'legacy',
                    isModernBuild: false,
                    title: isDevelopment ? features.TEST?.title ?? 'BetGames' : '<%- title %>',
                    chatConfig: isDevelopment
                        ? JSON.stringify(devConfig.chatClientConfig || null)
                        : '<%- chatConfig %>',
                    isDevelopment,
                };
            })(),
        }),
        new CopyWebpackPlugin({
            patterns: [
                {
                    from: require.resolve(`${sourceRoot}/templates/error.ejs`),
                    to: isDevelopment ? outputPath : `${outputPath}/views`,
                },
                {
                    from: require.resolve(`${sourceRoot}/templates/iframe.html`),
                    to: isDevelopment ? outputPath : `${outputPath}/views`,
                },
                {
                    from: require.resolve(`${sourceRoot}/templates/iframe_legacy.html`),
                    to: isDevelopment ? outputPath : `${outputPath}/views`,
                },
                {
                    from: require.resolve(`${sourceRoot}/templates/iframe-custom-integration.html`),
                    to: isDevelopment ? outputPath : `${outputPath}/views`,
                },
                {
                    from: require.resolve(
                        `${sourceRoot}/templates/iframe-custom-integration-outdated.html`,
                    ),
                    to: isDevelopment ? outputPath : `${outputPath}/views`,
                },
                {
                    from: require.resolve(`${sourceRoot}/templates/rgs.html`),
                    to: isDevelopment ? outputPath : `${outputPath}/views`,
                },
            ],
        }),
    );

    return webpackConfig;
};

export default config;
