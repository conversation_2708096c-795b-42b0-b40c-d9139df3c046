import path from 'path';
import express from 'express';
import { ProjectType } from '../enums/ProjectType';
import { IWebpackConfig } from '../interfaces/IWebpackConfig';
import {
    loginUser,
    registerUser,
} from '../../../apps/iframe-server/src/middlewares/chat-supabase/auth.middleware';

const iframeHtmlFile = path.resolve(
    __dirname,
    '../../../apps/iframe-shell/src/templates/iframe.html',
);
const iframeLegacyHtmlFile = path.resolve(
    __dirname,
    '../../../apps/iframe-shell/src/templates/iframe_legacy.html',
);
const iframeCustomIntegrationHtmlFile = path.resolve(
    __dirname,
    '../../../apps/iframe-shell/src/templates/iframe-custom-integration.html',
);
const iframeCustomIntegrationOutdatedHtmlFile = path.resolve(
    __dirname,
    '../../../apps/iframe-shell/src/templates/iframe-custom-integration-outdated.html',
);
const iframeRGS = path.resolve(__dirname, '../../../apps/iframe-shell/src/templates/rgs.html');

const webpackConfig: IWebpackConfig = (config, { options }) => {
    const envConfig = require(`${options.root}/configs/iframe/config.development.json`);

    const isHost = options.type === ProjectType.HOST;

    if (!config.devServer) {
        config.devServer = {};
    }
    if (!config.devServer.static) {
        config.devServer.static = [];
    }

    let devServerCustoms: typeof config.devServer = {
        static: [
            ...(Array.isArray(config.devServer?.static) ? config.devServer.static : []),
            {
                directory: 'dist/apps/loader',
                publicPath: '/public',
            },
            {
                directory: 'static/iframe',
                publicPath: '/static',
            },
        ],
    };

    if (isHost) {
        devServerCustoms = {
            ...devServerCustoms,
            server: {
                type: 'https',
            },
            historyApiFallback: true,
            client: {
                webSocketTransport: 'ws',
                overlay: false,
            },
            webSocketServer: 'ws',
            proxy: {
                '/player': {
                    target: envConfig.urls.videoPlayer,
                    changeOrigin: true,
                    ws: true,
                    secure: false,
                },
            },
            setupMiddlewares: (middlewares: any, devServer: any) => {
                // to avoid error while working with self certified supabase local instance
                process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

                devServer.app.use(express.json());
                devServer.app.use('/iframe', express.static(iframeHtmlFile));
                devServer.app.use('/iframe-legacy', express.static(iframeLegacyHtmlFile));
                devServer.app.use(
                    '/iframe-custom',
                    express.static(iframeCustomIntegrationHtmlFile),
                );
                devServer.app.use(
                    '/iframe-custom-outdated',
                    express.static(iframeCustomIntegrationOutdatedHtmlFile),
                );
                devServer.app.use('/rgs/*', express.static(iframeRGS));
                devServer.app.post('/chat/auth', loginUser);
                devServer.app.post('/chat/register', registerUser);
                return middlewares;
            },
        };
    }

    config.devServer = {
        ...config.devServer,
        ...devServerCustoms,
    };

    return config;
};

export default webpackConfig;
