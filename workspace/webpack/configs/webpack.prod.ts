import path from 'path';
import browserslist from 'browserslist';
import { RuleSetRule } from 'webpack';
import { BundleAnalyzerPlugin } from 'webpack-bundle-analyzer';
import { BundleStatsWebpackPlugin } from 'bundle-stats-webpack-plugin';
import CssMinimizerPlugin from 'css-minimizer-webpack-plugin';
import { sentryWebpackPlugin } from '@sentry/webpack-plugin';
import { IWebpackConfig } from '../interfaces/IWebpackConfig';
import { ProjectType } from '../enums/ProjectType';

const webpackConfig: IWebpackConfig = (config, { options, context }) => {
    if (!config.module || !context) {
        return config;
    }

    const isRemote = options.type === ProjectType.REMOTE;
    const browserslistConfig = browserslist.loadConfig({ path: options.root }) || [];

    config.resolve.alias = {
        ...config.resolve.alias,
        // making sure some packages are visible to the swc loader for further transpilation
        immer: path.resolve(options.root, 'node_modules', 'immer'),
        screenfull: path.resolve(options.root, 'node_modules', 'screenfull'),
    };

    config.cache = true;

    config.performance = {
        ...config.performance,
        maxEntrypointSize: 512000,
        maxAssetSize: 512000,
    };

    config.optimization = {
        ...config.optimization,
        emitOnErrors: true,
        minimize: true,
        minimizer: [
            '...', // Retain default minimizers like TerserPlugin
            new CssMinimizerPlugin({
                minimizerOptions: {
                    preset: ['default', { discardComments: { removeAll: true } }],
                },
            }),
        ],
        moduleIds: 'deterministic',
        splitChunks: {
            cacheGroups: {
                styles: {
                    test: /\.(css|scss)$/,
                    enforce: true,
                },
                supabase: {
                    test: /[\\/]node_modules[\\/]@supabase[\\/]/,
                    name: 'supabase-vendor',
                    chunks: 'async',
                    priority: 10,
                },
                vendor: {
                    test: /[\\/]node_modules[\\/]/,
                    name: 'vendor',
                    chunks: 'initial',
                },
            },
        },
    };

    (config.module.rules as RuleSetRule[])?.forEach((rule) => {
        // additional swc configs
        if (rule.test.toString() === '/\\.([jt])sx?$/') {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (rule.options as any).env = {
                coreJs: 3.22,
                mode: 'usage',
                targets: browserslistConfig,
            };

            rule.exclude = {
                and: [/node_modules/],
                // opt-in to transpile specific packages
                not: [/immer/, /screenfull/],
            };
        }
    });

    if (options.isReport) {
        config.plugins.push(
            ...[
                new BundleStatsWebpackPlugin({
                    json: true,
                    outDir: `${options.root}/reports/${context.projectName}`,
                }),
                new BundleAnalyzerPlugin({
                    analyzerMode: 'static',
                    analyzerPort: 4000,
                    openAnalyzer: false,
                    reportFilename: `${options.root}/reports/${context.projectName}/bundle-analyzer.html`,
                }),
            ],
        );
    }

    if (!isRemote && process.env.SENTRY_UPLOAD_SOURCEMAPS && process.env.SENTRY_AUTH_TOKEN) {
        config.plugins.push(
            ...[
                sentryWebpackPlugin({
                    authToken: process.env.SENTRY_AUTH_TOKEN,
                    org: 'betgames',
                    project: 'iframe',
                    release: {
                        name: process.env.SENTRY_RELEASE,
                        uploadLegacySourcemaps: {
                            paths: [`${options.outputPath}/web`],
                            ignore: ['node_modules', 'webpack.config.js'],
                        },
                    },
                }),
            ],
        );
    }

    return config;
};

export default webpackConfig;
