import { Configuration } from 'webpack';
import {
    ModuleFederationConfig,
    NormalizedWebpackExecutorOptions,
    NxWebpackExecutionContext,
    composePlugins,
} from '@nx/webpack';
import { NxAppWebpackPluginOptions } from '@nx/webpack/src/plugins/nx-webpack-plugin/nx-app-webpack-plugin-options';
import { ProjectType } from '../enums/ProjectType';

export interface IConfiguration extends Configuration {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    devServer?: { [key: string]: any };
}

interface IOptions {
    versionFile?: string;
}

export interface INxWebpackExecutionContext extends NxWebpackExecutionContext {
    options: NormalizedWebpackExecutorOptions & {
        type?: ProjectType;
        isTesting?: boolean;
        isReport?: boolean;
    };
}

export interface IWebpackConfig {
    (
        config: IConfiguration,
        ctx: Partial<INxWebpackExecutionContext>,
        envConfig: Partial<IEnvConfig>,
        workspaceOptions?: IOptions,
    ): IConfiguration;
}

export interface ICreateComposedPlugins {
    (config: {
        nxPluginOptions?: Partial<NxAppWebpackPluginOptions>;
        moduleFederationConfig?: ModuleFederationConfig;
        workspacesOptions?: IOptions;
    }): ReturnType<typeof composePlugins>;
}
