interface IEnvConfig {
    isDevelopment?: boolean;
}

declare module '*.scss' {
    const content: { [className: string]: string };
    export default content;
}

// eslint-disable-next-line @typescript-eslint/naming-convention
interface Window {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    piwikAbTestingAsyncInit: any;
    dataLayer: unknown[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    _paq: any;
    webkitAudioContext: typeof AudioContext;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    captain: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    classicConfig: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    hj: any;
    __REDUX_DEVTOOLS_EXTENSION__: unknown;
}

// eslint-disable-next-line @typescript-eslint/naming-convention
interface DateConstructor {
    systemNow: () => number;
}

declare interface ICallback<Params = unknown, Return = void> {
    (params?: Params): Return;
}

declare type IIconComponent = React.FunctionComponent<
    React.SVGProps<SVGSVGElement> & { title?: string; 'data-qa'?: string }
>;

declare const Piwik: unknown;

declare module '*.jpg';

declare module '*.png';

declare module '*.mp4' {
    const src: string;
    export default src;
}

declare module '*.svg' {
    export const ReactComponent: IIconComponent;

    const src: string;
    export default src;
}

declare module '*.svg?url' {
    const src: string;
    export default src;
}

interface IChatConfig {
    apiKey: string;
    region: string;
    games: Record<string, { channelId: string }>;
    supabase: {
        url: string;
        key: string;
    }
}

// eslint-disable-next-line no-underscore-dangle
declare const _globalConfig: {
    sentryRelease: string;
    environment: string;
    buildType: string;
    apiUrl: string;
    wsUrl: string;
    version: string;
    chatConfig?: IChatConfig;
};

// eslint-disable-next-line no-underscore-dangle
declare const _featuresConfig: {
    disableContextMenu: boolean;
    homeButtonNoRedirect: true;
    ddNewLayout: boolean;
    fullscreen: boolean;
    demoLabel: boolean;
    fallbackPartnerCode: string;
    homeButtonTarget: '_self' | '_top';
    licenseFooter: 'sni' | 'hwb';
    brandedStylesheet: string;
    brandedConfig: Record<string, Record<string, unknown>>;
    trackGameBettingOptions: number[];
    trackGameCasinoActions: number[];
    trackGameAmountsButtons: number[];
    trackClearAmountButton: boolean;
    trackMaxAmountButton: boolean;
    trackGamesNavigation: boolean;
    trackStatistics: boolean;
    trackPLSActions: boolean;
    gamesLogo: Record<number, string>;
    abTests: Array<{ name: string; percentage: number }>;
    abTestsVariation: Record<string, string>;
    showNonLiveLabel: number[];
    hwbSeparateCategory?: boolean;
};
