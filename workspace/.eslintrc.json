{"extends": ["../.eslintrc.json"], "parserOptions": {"project": ["workspace/tsconfig.json", "workspace/tsconfig.lib.json"]}, "settings": {"import/parsers": {"@typescript-eslint/parser": [".ts", ".tsx"]}, "import/resolver": {"typescript": {"project": "workspace/tsconfig.lib.json"}}}, "overrides": [{"files": ["*.ts", "*.tsx"], "rules": {"import/no-extraneous-dependencies": ["error", {"devDependencies": true}]}}], "eslint.workingDirectories": [{"directory": "apps", "changeProcessCWD": true}, {"directory": "libs", "changeProcessCWD": true}, {"directory": "libs/services/src/*", "changeProcessCWD": true}, {"directory": "libs/components/src/*", "changeProcessCWD": true}, {"directory": "configs", "changeProcessCWD": true}]}