{
    "root": true,
    "plugins": ["@nx"],
    "settings": {
        "import/resolver": {
            "node": {
                "extensions": [".js", ".jsx", ".ts", ".tsx"]
            },
            "typescript": {
                "project": "./tsconfig.base.json",
                "alwaysTryTypes": true
            }
        }
    },
    "rules": {
        "no-restricted-imports": [
            "error",
            {
                "paths": [
                    {
                        "name": "react",
                        "importNames": ["default"],
                        "message": "Please use named exports"
                    }
                ]
            }
        ]
    },
    "overrides": [
        {
            "files": ["*.ts", "*.tsx", "*.js", "*.jsx"],
            "extends": ["@betgames/eslint-config-client", "plugin:react/jsx-runtime"],
            "rules": {
                //"@nx/enforce-module-boundaries": [
                //    "error",
                //    {
                //        "enforceBuildableLibDependency": true,
                //        "allow": [],
                //        "depConstraints": [
                //            {
                //                "sourceTag": "*",
                //                "onlyDependOnLibsWithTags": ["*"]
                //            }
                //        ]
                //    }
                //]
            }
        },
        {
            "files": ["*.ts", "*.tsx"],
            "extends": ["plugin:@nx/typescript"],
            "rules": {
                "class-methods-use-this": 0,
                "jest/no-mocks-import": 0
            }
        },
        {
            "files": ["*.js", "*.jsx"],
            "extends": ["plugin:@nx/javascript"],
            "rules": {}
        },
        {
            "files": ["*.spec.ts", "*.spec.tsx", "*.spec.js", "*.spec.jsx"],
            "env": {
                "jest": true
            }
        }
    ]
}
