# Project Name
sonar.projectKey=<PERSON>rame
# this is the name and version displayed in the SonarQube UI. Was mandatory prior to SonarQube 6.1.
sonar.projectName=Iframe
sonar.projectVersion=1.0

# Path is relative to the sonar-project.properties file. Replace "\" by "/" on Windows.
# This property is optional if sonar.modules is set.
sonar.sources=./

sonar.tests=./
sonar.exclusions=**/coverage/**/*,**/dependency-check/**/*,**/.sonar/**/*,**/.scannerwork/**/*
sonar.coverage.exclusions=**/node_modules/**/*,**/libs/**/*,**/coverage/**/*,**/dependency-check/**/*

# Javascript Comma-delimited list of paths to LCOV coverage report files. Paths may be absolute or relative to project root.
sonar.javascript.lcov.reportPaths=./coverage/lcov.info
sonar.test.inclusions=src/**/*.spec.js,src/**/*.spec.jsx,src/**/*.test.js,src/**/*.test.jsx