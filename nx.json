{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{projectRoot}/.stylelintrc(.(json|yml|yaml|js))?"], "sharedGlobals": []}, "plugins": [{"plugin": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test"}}, {"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "test", "serveTargetName": "serve", "previewTargetName": "preview", "serveStaticTargetName": "serve-static"}}], "generators": {"@nx/react": {"application": {"babel": true, "style": "scss", "linter": "eslint", "bundler": "webpack"}, "component": {"style": "scss"}, "library": {"style": "scss", "linter": "eslint", "unitTestRunner": "jest"}}}, "targetDefaults": {"@nx/rollup:rollup": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@nx/esbuild:esbuild": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@nx/js:swc": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "build": {"inputs": ["production", "^production"]}, "@nx/webpack:webpack": {"inputs": [{"env": "NX_MF_DEV_SERVER_STATIC_REMOTES"}]}, "stylelint": {"inputs": ["default", "{workspaceRoot}/.stylelintrc(.(json|yml|yaml|js))?"], "cache": true}}}