import { Entity } from '@betgames/bg-state-manager';
import { GameId } from '@bg-shared/enums/GameId';
import HttpClientRepository from '@bg-shared/infrastructure/HttpClient/HttpClient.repository';
import { API_URLS } from '@bg-shared/constants/apiUrls';
import { IHowToPlayModel, IHowToPlayParams } from './interfaces';

export default class HowToPlayEntity extends Entity<IHowToPlayModel, GameId> {
    constructor(private readonly httpClient: HttpClientRepository) {
        super({
            key: 'HowToPlay',
            default: null,
        });
    }

    public updateHowToPlay(params: IHowToPlayParams): void {
        if (this.store(params.gameId).value) {
            return;
        }

        this.httpClient
            .get<IHowToPlayModel>(
                `${API_URLS.HOW_TO_PLAY}/${params.partnerCode}/${params.gameId}/${params.language}`,
            )
            .then((data) => {
                if (data) {
                    this.store(params.gameId).update(data);
                }
            });
    }
}
