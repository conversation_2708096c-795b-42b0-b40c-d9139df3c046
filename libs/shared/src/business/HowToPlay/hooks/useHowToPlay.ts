import { useEffect } from 'react';
import { useStore } from '@betgames/bg-state-manager';
import { IHowToPlayModel, IHowToPlayParams } from '@bg-shared/business/HowToPlay/interfaces';
import { howToPlayEntity } from '../main';

export const useHowToPlay = (params: IHowToPlayParams): IHowToPlayModel => {
    const data = useStore(howToPlayEntity.store(params.gameId));

    useEffect(() => {
        howToPlayEntity.updateHowToPlay(params);
    }, [params.gameId]);

    return data;
};
