import { JSX } from 'react';
import classNames from 'classnames';
import { RippleButton } from '@bg-components/RippleButton';
import { t, DealerLanguage } from '@bg-shared';
import { Image } from '@bg-components/Image';
import { Flag } from '../Flag';
import classes from './DealerLobbyItem.module.scss';

interface IProps {
    backgroundImage: string;
    title: string;
    onClick(): void;
    language: DealerLanguage;
    showInfo?: boolean;
    firstInList?: boolean;
}

const DEALER_CHANGE_TIME = '30';

export const DealerLobbyItem = (props: IProps): JSX.Element => (
    <div
        role="presentation"
        onClick={props.onClick}
        className={classNames(classes.container, { [classes.firstItem]: props.firstInList })}
    >
        <div className={classes.imageContainer}>
            <div
                className={classNames(classes.imageOverlay, {
                    [classes.biggerShadow]: props.showInfo,
                })}
            />
            <Image
                src={props.backgroundImage}
                alt="dealers"
                classNameContainer={classes.imageContainerOverride}
                className={classes.image}
            />
            <div className={classes.infoContainer}>
                <div className={classes.nameFlag}>
                    <div>{props.title}</div>
                    <Flag language={props.language} />
                </div>
                {props.showInfo && (
                    <div className={classes.info}>
                        {t.string('dealer_change_x_min').replace('{{count}}', DEALER_CHANGE_TIME)}
                    </div>
                )}
            </div>
        </div>
        <div className={classes.contentContainer}>
            <RippleButton className={classes.play}>{t.string('play')}</RippleButton>
        </div>
    </div>
);
