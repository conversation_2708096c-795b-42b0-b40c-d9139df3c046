import { JSX, useState } from 'react';
import classNames from 'classnames';
import { Scrollbar } from '@betgames/bg-tools';
import { useModalsManager, ModalName, sessionStore, GameId } from '@bg-shared';
import { Modal, ModalClose } from '@bg-components/Modal';
import { ButtonsList } from '@bg-components/ButtonsList';
import { QuestionIcon } from '@bg-components/Icon';
import { HowToPlayContent } from '@bg-components/HowToPlay';
import { IconButton, BurgerIcon } from '../Controls/IconButton';
import classes from './SettingsModal.module.scss';

interface ISettingsModalProps {
    gameId: GameId;
}

const enum PageName {
    Settings,
    HowToPlay,
}

export const SettingsModal = (props: ISettingsModalProps): JSX.Element => {
    const modalManager = useModalsManager();
    const [activePage, setActivePage] = useState<PageName>(PageName.HowToPlay);

    return (
        <Modal
            modalName={ModalName.RGS_SETTINGS}
            className={classes.modal}
            backdropClassName={classes.backdrop}
            showBackdrop
        >
            <div className={classes.modalContent}>
                <ButtonsList direction="column" className={classes.iconsContainer}>
                    {/* TODO https://betgamestv.atlassian.net/browse/RNG-776
                    <IconButton
                        Icon={SettingsIcon}
                        onClick={() => {
                            setActivePage(PageName.Settings);
                        }}
                        className={classNames(classes.icon, {
                            [classes.active]: activePage === PageName.Settings,
                        })}
                        data-qa="button-settings"
                    />
                    */}
                    <IconButton
                        Icon={QuestionIcon}
                        onClick={() => {
                            setActivePage(PageName.HowToPlay);
                        }}
                        className={classNames(classes.icon, {
                            [classes.active]: activePage === PageName.HowToPlay,
                        })}
                        data-qa="button-settings-how-to-play"
                    />
                    <IconButton
                        Icon={BurgerIcon}
                        onClick={() => {
                            modalManager.toggle(ModalName.RGS_SETTINGS, SettingsModal);
                        }}
                        className={classNames(classes.icon, classes.active)}
                        data-qa="button-settings-burger"
                    />
                </ButtonsList>
                {activePage === PageName.HowToPlay && (
                    <Scrollbar damping={1} alwaysShowTracks continuousScrolling>
                        <div>
                            <div className={classes.howToPlayContainer}>
                                <HowToPlayContent
                                    gameId={props.gameId}
                                    language={sessionStore.language}
                                    partnerCode={sessionStore.partnerCode}
                                />
                            </div>
                        </div>
                    </Scrollbar>
                )}
                {/* TODO https://betgamestv.atlassian.net/browse/RNG-776
                {activePage === PageName.Settings && <div>Settings</div>}
                */}
                <ModalClose
                    className={classes.close}
                    onClose={() => {
                        modalManager.close(ModalName.RGS_SETTINGS);
                    }}
                />
            </div>
        </Modal>
    );
};
