import { ComponentProps, JSX, useState } from 'react';
import classNames from 'classnames';
import { storageRepository, useAfterUpdate } from '@betgames/bg-tools';
import { Tooltip } from '@bg-components/Tooltip';
import { t } from '@bg-shared';
import { IconButton, TurboIcon } from '../IconButton';
import classes from './Turbo.module.scss';

interface IProps extends Pick<ComponentProps<typeof IconButton>, 'disabled' | 'onClick'> {
    active: boolean;
    hidden?: boolean;
}

const STORAGE_KEY = 'turboTooltipSeen';

export const Turbo = (props: IProps): JSX.Element => {
    const [tooltipSeen, setTooltipSeen] = useState<boolean>(
        storageRepository.getItemParsed<boolean>(STORAGE_KEY) ?? false,
    );

    useAfterUpdate(() => {
        if ((props.active && props.disabled) || (!props.active && !props.disabled)) {
            setTooltipSeen(true);
            storageRepository.setItemParsed(STORAGE_KEY, true);
        }
    }, [props.active, props.disabled]);

    return (
        <>
            {props.active && !props.disabled && !tooltipSeen && (
                <Tooltip
                    clickable
                    place="top"
                    anchorSelect={`.${classes.container}`}
                    isOpen
                    opacity={1}
                >
                    {t.string('double_speed_mode_on')}
                </Tooltip>
            )}
            <IconButton
                Icon={TurboIcon}
                onClick={props.onClick}
                border
                className={classNames(classes.container, {
                    [classes.active]: props.active,
                    [classes.hidden]: props.hidden,
                })}
                disabled={props.disabled}
                dataQa={props.active ? 'button-turbo-active' : 'button-turbo'}
            />
        </>
    );
};
