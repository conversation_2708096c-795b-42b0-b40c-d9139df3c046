import { JSX } from 'react';
import { GameId, ModalName, useModalsManager } from '@bg-shared';
import { SettingsModal } from '../../SettingsModal';
import { IconButton, BurgerIcon } from '../IconButton';
import classes from './Burger.module.scss';

interface IBurgerProps {
    gameId: GameId;
}

export const Burger = ({ gameId }: IBurgerProps): JSX.Element => {
    const modalManager = useModalsManager();

    return (
        <IconButton
            Icon={BurgerIcon}
            onClick={() => {
                modalManager.toggle(ModalName.RGS_SETTINGS, SettingsModal, { gameId });
            }}
            className={classes.container}
            dataQa="button-burger"
        />
    );
};
