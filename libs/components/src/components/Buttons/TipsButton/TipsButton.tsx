import { JSX, useState, useEffect } from 'react';
import classNames from 'classnames';
import { useStore } from '@betgames/bg-state-manager';
import { TipsIcon } from '@bg-components/Icon';
import { Tooltip } from '@bg-components/Tooltip';
import {
    gamesStatusEntity,
    GameId,
    selectedChipStore,
    useMobileView,
    TOOLTIP_DURATION,
    t,
    balanceStore,
    balanceSelectors,
    gamesMessagesEntity,
    parseErrorText,
    NOTIFICATION_ERROR_SHOW_TIME,
    GameMessageType,
    gamesStatusSelectors,
} from '@bg-shared';
import { tipsAPI } from '@bg-shared/business/Tips';
import { TipsAnimation } from './TipsAnimation';
import { ControlButton } from '../ControlButton';
import localClasses from './TipsButton.module.scss';

interface IProps {
    gameId: GameId;
    runId: number;
    className?: string;
}

const tooltipSelector = 'tips-button';

export const TipsButton = (props: IProps): JSX.Element => {
    const isMobileView = useMobileView();
    const openForBets = useStore(
        gamesStatusEntity.store(props.gameId),
        gamesStatusSelectors.getOpenForBets,
    );
    const selectedChip = useStore(selectedChipStore);
    const balance = useStore(balanceStore, balanceSelectors.getValue(props.gameId));
    const [hidden, setHidden] = useState<boolean>(!openForBets);
    const [animating, setAnimating] = useState<boolean>(false);
    const [processing, setProcessing] = useState<boolean>(false);
    const [isThanksTooltipOpen, setIsThanksTooltipOpen] = useState<boolean>(false);

    const onClick = () => {
        if (animating || processing || !openForBets) {
            return;
        }

        setAnimating(true);
        setProcessing(true);
        tipsAPI
            .placeTip(props.gameId, selectedChip, props.runId)
            .catch((e) => {
                gamesMessagesEntity.showMessage({
                    message: parseErrorText(e),
                    timeout: NOTIFICATION_ERROR_SHOW_TIME,
                    type: GameMessageType.INFO,
                });
            })
            .finally(() => {
                setProcessing(false);
            });
    };

    const onFinish = () => {
        setAnimating(false);
        setIsThanksTooltipOpen(true);
    };

    useEffect(() => {
        if ((animating && !openForBets) || isThanksTooltipOpen) {
            return;
        }

        setHidden(!openForBets);
    }, [openForBets, animating, isThanksTooltipOpen]);

    return (
        <>
            <ControlButton
                className={classNames(localClasses.container, props.className, {
                    [localClasses.hidden]: hidden,
                })}
                onClick={onClick}
                dataQa={tooltipSelector}
                disabled={!openForBets || processing || selectedChip > balance}
            >
                {animating ? <TipsAnimation onFinish={onFinish} /> : <TipsIcon />}
            </ControlButton>
            {!isMobileView && !animating && !isThanksTooltipOpen && (
                <Tooltip
                    place="top-end"
                    anchorSelect={`[data-qa="${tooltipSelector}"]`}
                    content={t
                        .string('tips_info_tooltip')
                        .replace('{{count}}', String(selectedChip))}
                />
            )}
            <Tooltip
                place={isMobileView ? 'bottom-end' : 'top-end'}
                isOpen={!animating && isThanksTooltipOpen}
                duration={TOOLTIP_DURATION}
                onTimeout={() => {
                    setIsThanksTooltipOpen(false);
                }}
                anchorSelect={`[data-qa="${tooltipSelector}"]`}
                content={t.string('tips_thanks_tooltip')}
            />
        </>
    );
};
