import { JSX } from 'react';
import classNames from 'classnames';
import { t, MAX_PICK_X_LINES_PER_BET, EMPTY_SPACE } from '@bg-shared';
import classes from './BettingOptionsLimitWarning.module.scss';

interface IProps {
    isSelectionExceededLimits?: boolean;
    className?: string;
}
export const BettingOptionsLimitWarning = (props: IProps): JSX.Element => (
    <div className={classNames(classes.warning, props.className)}>
        {t.string('pick_x_lines_limit').replace('{{X}}', `${MAX_PICK_X_LINES_PER_BET}`)}
        {props.isSelectionExceededLimits && `${EMPTY_SPACE}${t.string('remove_line_to_continue')}`}
    </div>
);
