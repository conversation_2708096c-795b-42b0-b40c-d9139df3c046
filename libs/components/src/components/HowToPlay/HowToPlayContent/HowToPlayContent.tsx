import { JSX } from 'react';
import { HowToPlaySkeleton } from '@bg-components/HowToPlay/HowToPlaySkeleton';
import { t, useHowToPlay, IHowToPlayParams } from '@bg-shared';
import { HowToPlaySection } from '../HowToPlaySection';
import classes from './HowToPlayContent.module.scss';

export const HowToPlayContent = (props: IHowToPlayParams): JSX.Element => {
    const content = useHowToPlay(props);

    if (!content) {
        return <HowToPlaySkeleton showHeader={false} />;
    }

    return (
        <>
            {!content.blocks.length && (
                <div data-qa="area-htp-default-content">
                    <p className={classes.defaultHeader}>{t.string('oops')}</p>
                    <hr />
                    <p className={classes.defaultText}>{t.string('empty_content')}</p>
                </div>
            )}
            {content.blocks
                .filter((item) => item.content?.length)
                .map((item, index) => (
                    <HowToPlaySection key={index} howToPlayData={item} />
                ))}
        </>
    );
};
