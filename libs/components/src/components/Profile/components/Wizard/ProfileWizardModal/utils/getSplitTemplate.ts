import { JSX } from 'react'; /**
 * Split the string with occurrences "{{<word>}}", replacing the <word> with the value processed by `replacer()`
 * If no occurrences found, whole original string returns
 *
 * Examples:
 * "Click {{here}}!" => ["Click ", {<a type="button">here</a>}, "!"]
 * (value) => <a type="button">{value}</a>
 */
export const getSplitTemplate = (
    string: string,
    replacer: (str: string) => JSX.Element,
): (string | JSX.Element)[] => {
    const searchPattern = /{{[^}}]+}}/g;
    const splitTemplate = [];
    let lastSearchIndex = 0;
    let searchResult;

    while ((searchResult = searchPattern.exec(string))) {
        splitTemplate.push(string.slice(lastSearchIndex, searchResult.index));
        splitTemplate.push(replacer(searchResult[0].slice(2, -2)));

        lastSearchIndex = searchPattern.lastIndex;
    }

    if (lastSearchIndex < string.length) {
        splitTemplate.push(string.slice(lastSearchIndex));
    }

    return splitTemplate;
};
