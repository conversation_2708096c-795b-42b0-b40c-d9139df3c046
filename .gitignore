# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
dist
tmp
/out-tsc

# dependencies
node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/reports

# System Files
.DS_Store
Thumbs.db

.nx/

# security files
*.pem

# production build
/production

# configs
/configs/**/config.development.json
.env
.env.*
!.env.example

.cursor/rules/nx-rules.mdc
.github/instructions/nx.instructions.md