#!/usr/bin/env node
/* eslint-disable */
import { readdir, mkdir, copyFile, stat, rename, rm } from 'node:fs/promises';
import { existsSync, statSync } from 'node:fs';
import { join, basename, extname } from 'node:path';
import { spawn } from 'node:child_process';

// Get source directory from command line arguments
const args = process.argv.slice(2);
if (args.length === 0) {
  console.error('Usage: node encode-images.mjs <source-directory>');
  console.error('Example: node encode-images.mjs ./image-files');
  process.exit(1);
}

const SRC_DIR = args[0];
const OUT_DIR = 'dist';

// Validate source directory exists
if (!existsSync(SRC_DIR)) {
  console.error(`Error: Source directory '${SRC_DIR}' does not exist`);
  process.exit(1);
}

await mkdir(OUT_DIR, { recursive: true });

function slugify(name) {
  return name
    .toLowerCase()
    .replace(/\+/g, '-plus-')
    .replace(/&/g, '-and-')
    .replace(/\s+/g, '-')            // spaces -> dash
    .replace(/[^a-z0-9._-]/g, '')    // remove other chars
    .replace(/-+/g, '-')             // collapse dashes
    .replace(/^-|-$/g, '')           // trim
    .replace(/\./g, '-')             // dots -> dashes
    .replace(/_/g, '-');             // underscores -> dashes
}

function ffmpegEncode(input, outputArgs) {
  return new Promise((resolve, reject) => {
    const args = ['-y', '-hide_banner', '-loglevel', 'error', '-i', input, ...outputArgs];
    const proc = spawn('ffmpeg', args, { stdio: 'inherit' });
    proc.on('exit', code => {
      if (code === 0) resolve();
      else reject(new Error(`ffmpeg exited with code ${code}`));
    });
  });
}

function getFileSize(filePath) {
  try {
    const stats = statSync(filePath);
    return stats.size;
  } catch {
    return 0;
  }
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

console.log(`Processing image files from: ${SRC_DIR}`);
console.log(`Output directory: ${OUT_DIR}`);

const entries = await readdir(SRC_DIR);
let processedCount = 0;

for (const entry of entries) {
  const ext = extname(entry).toLowerCase();
  const isJpg = ext === '.jpg' || ext === '.jpeg';
  const isPng = ext === '.png';
  if (!isJpg && !isPng) continue;

  const fullPath = join(SRC_DIR, entry);
  const st = await stat(fullPath);
  if (!st.isFile()) continue;

  const base = basename(entry, ext);
  let safe = slugify(base);

  // ensure uniqueness
  let counter = 1;
  while (
    ['.jpg', '.png', '.webp', '.avif'].some(ext => existsSync(join(OUT_DIR, safe + ext)))
  ) {
    safe = `${slugify(base)}-${counter++}`;
  }

  // Copy original file
  const originalExt = isJpg ? '.jpg' : '.png';
  const originalCopy = join(OUT_DIR, safe + originalExt);
  await copyFile(fullPath, originalCopy);

  const originalSize = st.size;
  console.log(`\nProcessing: ${entry} (${formatFileSize(originalSize)})`);

  /* ------------------------------------------------------------------ */
  /* JPG optimisation (only for original JPG files) */
  /* ------------------------------------------------------------------ */
  if (isJpg) {
    const jpgOut = join(OUT_DIR, safe + '.jpg');
    console.log(`Encoding optimised JPG: ${entry} -> ${safe}.jpg`);
    await ffmpegEncode(fullPath, [
      '-c:v', 'mjpeg',
      '-q:v', '60',      // Quality 0-100 – 60 is a good trade-off
      '-pix_fmt', 'yuv420p',
      jpgOut,
    ]);
  }

  /* ------------------------------------------------------------------ */
  /* PNG optimisation (only for original PNG files) */
  /* ------------------------------------------------------------------ */
  if (isPng) {
    const pngOut = join(OUT_DIR, safe + '.png');
    const tmpPng = join(OUT_DIR, safe + '.tmp.png');
    console.log(`Optimising PNG: ${entry} -> ${safe}.png`);

    await ffmpegEncode(fullPath, [
      '-c:v', 'png',
      '-compression_level', '9',   // max compression
      '-pred', '5',                // mixed predictor for better result
      '-pix_fmt', 'rgba',
      tmpPng,
    ]);

    const origSize = getFileSize(fullPath);
    const newSize = getFileSize(tmpPng);

    if (newSize < origSize) {
      await rename(tmpPng, pngOut);
      console.log(`   ✅ PNG optimised: ${formatFileSize(origSize)} → ${formatFileSize(newSize)}`);
    } else {
      await rm(tmpPng);
      await copyFile(fullPath, pngOut);
      console.log(`   ⚠️  Optimised PNG was larger; kept original (${formatFileSize(origSize)})`);
    }
  }

  /* ------------------------------------------------------------------ */
  /* WebP generation (preserve alpha for PNGs) */
  /* ------------------------------------------------------------------ */
  const webpOut = join(OUT_DIR, safe + '.webp');
  if (!existsSync(webpOut)) {
    console.log(`Encoding WebP: ${entry} -> ${safe}.webp`);
    await ffmpegEncode(fullPath, [
      '-c:v', 'libwebp',
      '-quality', '80', // WebP quality
      '-compression_level', '6',
      '-pix_fmt', isPng ? 'yuva420p' : 'yuv420p', // keep alpha for PNGs
      webpOut,
    ]);
  } else {
    console.log(`Skipping existing WebP: ${safe}.webp`);
  }

  /* ------------------------------------------------------------------ */
  /* AVIF generation (modern format, alpha if available) */
  /* ------------------------------------------------------------------ */
  const avifOut = join(OUT_DIR, safe + '.avif');
  console.log(`Encoding AVIF: ${entry} -> ${safe}.avif`);
  await ffmpegEncode(fullPath, [
    '-c:v', 'libaom-av1',
    '-still-picture', '1',
    '-crf', isPng ? '22' : '28',       // better quality for PNGs with alpha
    '-b:v', '0',
    '-usage', 'good',
    '-cpu-used', '4',                  // a bit faster
    '-row-mt', '1',
    '-tiles', '2x2',
    '-g', '1',
    '-pix_fmt', isPng ? 'yuva444p' : 'yuv420p',
    ...(isPng ? ['-alpha_quality', '80'] : []),
    avifOut,
  ]);

  processedCount++;
}

console.log(`\nAll done! Processed ${processedCount} image files.`);
console.log(`Output files are in: ${OUT_DIR}`);
