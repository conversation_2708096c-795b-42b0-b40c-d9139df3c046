#!/usr/bin/env node
import { readdir, mkdir, copyFile, stat } from 'node:fs/promises';
import { existsSync } from 'node:fs';
import { join, basename } from 'node:path';
import { spawn } from 'node:child_process';

// Get source directory from command line arguments
const args = process.argv.slice(2);
if (args.length === 0) {
  console.error('Usage: node encode-audio.mjs <source-directory>');
  console.error('Example: node encode-audio.mjs ./audio-files');
  process.exit(1);
}

const SRC_DIR = args[0];
const OUT_DIR = 'dist';

// Validate source directory exists
if (!existsSync(SRC_DIR)) {
  console.error(`Error: Source directory '${SRC_DIR}' does not exist`);
  process.exit(1);
}

await mkdir(OUT_DIR, { recursive: true });

function slugify(name) {
  return name
    .toLowerCase()
    .replace(/\+/g, '-plus-')
    .replace(/&/g, '-and-')
    .replace(/\s+/g, '-')            // spaces -> dash
    .replace(/[^a-z0-9._-]/g, '')    // remove other chars
    .replace(/-+/g, '-')             // collapse dashes
    .replace(/^-|-$/g, '')           // trim
    .replace(/\./g, '-')             // dots -> dashes
    .replace(/_/g, '-');             // underscores -> dashes
}

function ffmpegEncode(input, outputArgs) {
  return new Promise((resolve, reject) => {
    const args = ['-y', '-hide_banner', '-loglevel', 'error', '-i', input, ...outputArgs];
    const proc = spawn('ffmpeg', args, { stdio: 'inherit' });
    proc.on('exit', code => {
      if (code === 0) resolve();
      else reject(new Error(`ffmpeg exited with code ${code}`));
    });
  });
}

console.log(`Processing WAV files from: ${SRC_DIR}`);
console.log(`Output directory: ${OUT_DIR}`);

const entries = await readdir(SRC_DIR);
let processedCount = 0;

for (const entry of entries) {
  if (!entry.toLowerCase().endsWith('.wav')) continue;
  const fullPath = join(SRC_DIR, entry);
  const st = await stat(fullPath);
  if (!st.isFile()) continue;

  const base = basename(entry, '.wav');
  let safe = slugify(base);

  // ensure uniqueness
  let counter = 1;
  while (
    ['.wav', '.mp3', '.webm'].some(ext => existsSync(join(OUT_DIR, safe + ext)))
  ) {
    safe = `${slugify(base)}-${counter++}`;
  }

  const wavCopy = join(OUT_DIR, safe + '.wav');
  await copyFile(fullPath, wavCopy);

  const mp3Out = join(OUT_DIR, safe + '.mp3');
  const webmOut = join(OUT_DIR, safe + '.webm');

  if (!existsSync(mp3Out)) {
    console.log(`Encoding MP3: ${entry} -> ${safe}.mp3`);
    await ffmpegEncode(wavCopy, ['-c:a', 'libmp3lame', '-b:a', '96k', '-ar', '44100', mp3Out]);
  } else {
    console.log(`Skipping existing MP3: ${safe}.mp3`);
  }

  if (!existsSync(webmOut)) {
    console.log(`Encoding WEBM (Opus): ${entry} -> ${safe}.webm`);
    await ffmpegEncode(wavCopy, ['-c:a', 'libopus', '-b:a', '56k', '-ar', '48000', webmOut]);
  } else {
    console.log(`Skipping existing WEBM: ${safe}.webm`);
  }

  processedCount++;
}

console.log(`\nAll done! Processed ${processedCount} WAV files.`);
