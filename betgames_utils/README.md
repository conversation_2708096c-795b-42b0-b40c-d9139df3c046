# BetGames Utils

Utility scripts for processing audio and image files with compression and format conversion.

## Prerequisites

Both scripts require `ffmpeg` to be installed on your system:

### macOS
```bash
brew install ffmpeg
```

### Ubuntu/Debian
```bash
sudo apt update
sudo apt install ffmpeg
```

### Windows
Download from [ffmpeg.org](https://ffmpeg.org/download.html) or install via Chocolatey:
```bash
choco install ffmpeg
```

## Audio Encoding Script

Converts WAV files to MP3 and WebM (Opus) formats with compression.

### Usage
```bash
node encode-audio.mjs <source-directory>
```

### Example
```bash
node encode-audio.mjs ./audio-files
```

### Output
- Creates a `dist/` directory
- Converts each WAV file to:
  - MP3 (96kbps, 44.1kHz)
  - WebM/Opus (56kbps, 48kHz)
- Preserves original WAV files

## Image Encoding Script

Converts JPG/PNG images to multiple formats with compression similar to TinyPNG.

### Usage
```bash
node encode-images.mjs <source-directory>
```

### Example
```bash
node encode-images.mjs ./image-files
```

### Output
- Creates a `dist/` directory
- Converts each image to:
  - **JPG**: Quality 85 (good balance of size/quality)
  - **PNG**: Compression level 6 (optimized)
  - **WebP**: Quality 80, compression level 6 (high compression)
  - **AVIF**: CRF 30, medium preset (modern format, excellent compression)

### Compression Settings

The script uses settings optimized for web use:

- **JPG**: Quality 85 - Good balance between file size and visual quality
- **PNG**: Compression level 6 - Optimized for web delivery
- **WebP**: Quality 80, compression level 6 - Excellent compression with good quality
- **AVIF**: CRF 30, medium preset - Modern format with superior compression

### Features

- **Safe filenames**: Converts special characters to URL-safe names
- **Duplicate handling**: Automatically adds numbers to prevent filename conflicts
- **Skip existing**: Won't re-encode files that already exist
- **Progress reporting**: Shows file sizes and processing status
- **Error handling**: Graceful handling of encoding failures

### File Size Comparison

Typical compression ratios compared to original:
- **JPG**: 60-80% of original size
- **PNG**: 70-90% of original size (depends on image content)
- **WebP**: 30-50% of original size
- **AVIF**: 20-40% of original size

## Tips

1. **Batch processing**: Place all your images/audio in a single directory
2. **Backup originals**: Scripts preserve original files in the output directory
3. **Web optimization**: Use WebP/AVIF for modern browsers, JPG/PNG for fallbacks
4. **Quality vs size**: Adjust quality settings in the script if needed for your use case 