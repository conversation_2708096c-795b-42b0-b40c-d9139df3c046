<svg width="111" height="70" viewBox="0 0 111 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_556_24549)">
<path d="M103.637 32.6972L109.075 32.5733C109.586 32.5617 110.01 32.9665 110.021 33.4775L110.077 35.9384C110.089 36.4494 109.684 36.8732 109.173 36.8848L103.736 37.0088C103.225 37.0206 102.802 36.6157 102.79 36.1047L102.734 33.6437C102.722 33.1327 103.127 32.709 103.637 32.6972Z" fill="url(#paint0_linear_556_24549)"/>
<g filter="url(#filter0_i_556_24549)">
<path d="M73.1856 68.2014C68.8153 66.8777 66.3462 62.2603 67.6705 57.8879C68.9948 53.5158 73.611 51.0445 77.9813 52.3682C82.3511 53.6918 84.8202 58.3091 83.4959 62.6813C82.1715 67.0537 77.5553 69.525 73.1856 68.2014Z" fill="url(#paint1_radial_556_24549)"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M93.0938 20.1521L93.864 48.9135C93.864 48.9135 67.7531 47.5365 52.4557 45.2696C39.3024 43.3204 37.0918 42.8957 21.9885 37.4563C21.6308 38.4758 20.4135 41.5196 18.5134 41.9112C17.2315 42.1754 16.0133 42.3567 15.2146 41.3199C14.6893 40.638 15.1379 40.0153 15.6269 39.3365C16.0381 38.7656 16.4779 38.1551 16.3908 37.4364C16.2746 36.4758 15.4973 35.1253 15.4973 35.1253L15.8408 35.2293C15.6687 35.1668 15.4953 35.1038 15.3205 35.0402C7.67376 32.2611 2.4339 30.1184 1.61734 26.4759C0.800798 22.8334 1.79219 13.875 2.71791 11.1785C3.84258 7.90229 8.65449 -1.24128 15.9415 0.965874C27.6057 4.49886 32.6993 20.2595 32.6993 20.2595L37.6761 20.082L93.0938 20.1521Z" fill="url(#paint2_linear_556_24549)"/>
<g filter="url(#filter1_i_556_24549)">
<path d="M93.864 48.9135L93.0938 20.1521L37.6761 20.082L32.6993 20.2595C32.6993 20.2595 27.6057 4.49886 15.9415 0.965874C8.65449 -1.24128 3.84258 7.90229 2.71791 11.1785C1.79219 13.875 0.800798 22.8334 1.61734 26.4759C2.4339 30.1184 7.67376 32.2611 15.3205 35.0402C36.7 42.8104 37.5758 43.0645 52.4557 45.2696C67.7531 47.5365 93.864 48.9135 93.864 48.9135Z" fill="url(#paint3_linear_556_24549)"/>
</g>
<g filter="url(#filter2_i_556_24549)">
<path d="M104.805 25.3231L105.211 44.3764C105.278 47.5115 102.779 50.1167 99.6418 50.1836L93.1523 50.3217L92.5039 19.8967L98.9935 19.7585C102.131 19.6916 104.738 22.1783 104.805 25.3134L104.805 25.3231Z" fill="url(#paint4_linear_556_24549)"/>
</g>
<path d="M92.4958 19.912L98.8959 19.7667C102.142 19.6929 104.833 22.2647 104.907 25.5109L105.349 44.9379C105.422 48.1839 102.85 50.8753 99.6043 50.949L93.2041 51.0943L92.4958 19.912Z" fill="url(#paint5_linear_556_24549)" fill-opacity="0.2"/>
<g filter="url(#filter3_d_556_24549)">
<path d="M92.4958 19.912L98.8959 19.7667C102.142 19.6929 104.833 22.2647 104.907 25.5109L105.33 44.1564C105.404 47.4027 102.833 50.0941 99.5866 50.1678L93.1864 50.3131L92.4958 19.912Z" fill="url(#paint6_linear_556_24549)" fill-opacity="0.01"/>
</g>
<path d="M60.4579 12.7393L98.0964 12.6066C98.3565 12.6058 98.5661 12.3944 98.565 12.1346L98.5499 7.77526C98.5487 7.51543 98.3376 7.30567 98.0775 7.30649L60.4392 7.43921C60.1794 7.44015 59.9696 7.65143 59.9705 7.91119L59.9859 12.2706C59.9868 12.5303 60.1981 12.7402 60.4579 12.7393Z" fill="url(#paint7_linear_556_24549)"/>
<path d="M70.9596 58.8842C70.1852 61.4408 71.6274 64.137 74.1825 64.911C76.7376 65.6849 79.4329 64.2418 80.2073 61.6852C80.9816 59.1287 79.5401 56.4326 76.9849 55.6587C74.4298 54.8847 71.734 56.3276 70.9596 58.8842Z" fill="url(#paint8_radial_556_24549)" fill-opacity="0.5"/>
<path d="M52.3281 55.0725L53.9441 55.562C55.5235 56.0404 92.9004 54.2483 93.695 54.1195C94.4166 54.0028 94.4259 53.467 94.4213 53.3686L94.4205 53.3531C94.4205 53.3531 94.4207 53.3585 94.4213 53.3686L94.7697 58.4772C94.7697 58.4772 94.658 59.2618 93.7624 59.3062C92.8668 59.3507 55.9454 61.1723 54.3226 60.9688C52.699 60.7658 52.4758 60.4095 52.4758 60.4095L52.3281 55.0725Z" fill="url(#paint9_linear_556_24549)"/>
<path d="M31.8424 18.3007L14.4834 21.0716L32.6453 21.5572L31.8424 18.3007Z" fill="url(#paint10_radial_556_24549)"/>
<path d="M27.1505 28.6269L26.9956 27.584L24.5967 26.8574L25.0203 29.4189L27.1505 28.6269Z" fill="url(#paint11_linear_556_24549)"/>
<g filter="url(#filter4_i_556_24549)">
<path d="M4.57133 27.7038C4.57167 27.671 4.59332 27.6447 4.62608 27.6455C5.55209 27.6674 18.0782 27.9611 18.9257 27.8826C19.7486 27.8063 23.9969 26.9878 24.5453 26.8818C24.5777 26.8755 24.6082 26.8974 24.6136 26.93L25.021 29.3625C25.0262 29.3933 25.0089 29.4222 24.9784 29.429C24.4009 29.5564 19.1877 30.7056 18.8607 30.7291C17.8955 30.7984 5.50179 30.4809 4.59168 30.4575C4.55956 30.4567 4.53928 30.4303 4.53962 30.3982L4.57133 27.7038Z" fill="url(#paint12_linear_556_24549)"/>
</g>
<g filter="url(#filter5_i_556_24549)">
<path d="M10.3887 26.633L24.5704 26.8847L18.8889 27.9718L4.55556 27.6514L10.3887 26.633Z" fill="url(#paint13_linear_556_24549)"/>
</g>
<path d="M20.711 27.6138L24.6019 26.8655L25.0337 29.4213L20.8999 30.2929L20.711 27.6138Z" fill="url(#paint14_linear_556_24549)" fill-opacity="0.5"/>
<path d="M76.4258 28.9966C84.7367 29.2642 91.5408 27.3941 91.6234 24.8196C91.7065 22.2452 85.0364 19.9413 76.7261 19.6738C68.4152 19.4062 61.6111 21.2763 61.5285 23.8509C61.4454 26.4252 68.1155 28.7292 76.4258 28.9966Z" fill="url(#paint15_radial_556_24549)" fill-opacity="0.7"/>
<path d="M91.0667 35.6277C90.9798 43.7267 92.1379 50.3055 93.654 50.3218C95.1695 50.338 96.4686 43.7856 96.5554 35.6865C96.6423 27.5874 95.4842 21.0087 93.9681 20.9923C92.4526 20.9761 91.1535 27.5286 91.0667 35.6277Z" fill="url(#paint16_radial_556_24549)" fill-opacity="0.6"/>
<g opacity="0.5" filter="url(#filter6_d_556_24549)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M93.1856 50.3211L92.5037 19.9113L92.8037 19.9047L93.4849 50.3144L93.1856 50.3211Z" fill="url(#paint17_linear_556_24549)"/>
</g>
<g filter="url(#filter7_i_556_24549)">
<path d="M87.5947 16.6898L67.1616 16.145L60.3882 12.7616L60.0123 12.3883L98.5771 12.1369L98.3541 12.5323L87.5947 16.6898Z" fill="url(#paint18_linear_556_24549)"/>
</g>
<path d="M80.3814 15.8526C86.8978 15.9626 92.1912 15.4474 92.204 14.7017C92.2167 13.956 86.9436 13.2621 80.4267 13.152C73.9103 13.042 68.6168 13.5572 68.6041 14.3029C68.5914 15.0486 73.8645 15.7425 80.3814 15.8526Z" fill="url(#paint19_radial_556_24549)" fill-opacity="0.6"/>
<g filter="url(#filter8_i_556_24549)">
<path d="M52.4211 55.0589L60.4505 43.5821C60.4724 43.5504 60.5088 43.5317 60.5474 43.5319L91.7223 43.6951C91.7751 43.6953 91.8213 43.7311 91.8352 43.7826L94.4517 53.6875C94.4594 53.7167 94.4556 53.7477 94.4408 53.7741L94.046 54.4915C94.0266 54.5273 93.9895 54.5501 93.949 54.5522L74.3836 55.5779L54.1116 55.7264C54.0993 55.7263 54.0876 55.7246 54.0763 55.7212L52.4834 55.2387C52.4068 55.2155 52.3749 55.1242 52.4211 55.0589Z" fill="url(#paint20_linear_556_24549)"/>
</g>
<path d="M52.4211 55.0589L60.4505 43.5821C60.4724 43.5504 60.5088 43.5317 60.5474 43.5319L91.7217 43.695C91.7751 43.6953 91.8213 43.7311 91.8352 43.7827L94.4284 53.6245C94.4363 53.6551 94.432 53.6877 94.4152 53.7148L94.1947 54.0837C94.1749 54.1166 94.1408 54.138 94.1025 54.1407L74.3837 55.5779L54.1116 55.7264C54.0993 55.7263 54.0876 55.7246 54.0763 55.7212L52.4834 55.2387C52.4068 55.2155 52.3749 55.1242 52.4211 55.0589Z" fill="url(#paint21_linear_556_24549)"/>
<path d="M63.9149 32.0675L89.5786 32.5927L91.8074 43.7369L60.4229 43.5962L63.9149 32.0675Z" fill="url(#paint22_linear_556_24549)" fill-opacity="0.8"/>
<g filter="url(#filter9_i_556_24549)">
<path d="M83.6923 49.0295L84.9319 14.8272L75.8563 14.2346C75.8563 14.2346 78.2306 24.1522 77.0998 30.6074C75.6718 38.7617 66.7918 49.2623 66.7918 49.2623L83.6923 49.0295Z" fill="url(#paint23_linear_556_24549)"/>
</g>
<path d="M32.2602 29.2713C32.4501 29.2143 32.6521 29.1682 32.8652 29.1342C33.079 29.1008 33.3235 29.0835 33.5992 29.0828C34.1028 29.0815 34.5545 29.1709 34.9542 29.3499C35.3533 29.5287 35.6895 29.7747 35.9608 30.0864C36.2321 30.3987 36.4397 30.7684 36.5832 31.1948C36.7273 31.6212 36.7993 32.076 36.8005 32.5594C36.8022 33.204 36.7058 33.7641 36.5126 34.239C36.3187 34.7138 36.0466 35.1053 35.6957 35.4147C35.3449 35.7242 34.9296 35.9553 34.4508 36.1076C33.9708 36.2606 33.4467 36.3384 32.8768 36.3399C32.2502 36.3416 31.684 36.2884 31.1802 36.1811C30.6763 36.0731 30.211 35.9393 29.7823 35.7791L29.7588 26.703L32.2529 26.6966L32.2596 29.2713L32.2602 29.2713ZM32.2736 34.4204C32.378 34.4483 32.4804 34.4693 32.5803 34.4838C32.6802 34.4977 32.7961 34.5045 32.9294 34.5041C33.357 34.503 33.7031 34.353 33.9683 34.0534C34.2335 33.7545 34.3656 33.2959 34.364 32.6795C34.3624 32.0817 34.2305 31.6418 33.9683 31.3578C33.7067 31.0737 33.3761 30.9325 32.9768 30.9335C32.8435 30.9339 32.7205 30.9458 32.6059 30.9699C32.492 30.9933 32.3781 31.0245 32.2642 31.0627L32.273 34.4203L32.2736 34.4204ZM39.5243 33.1636C39.544 33.5807 39.6801 33.9055 39.9331 34.1376C40.1855 34.3695 40.5059 34.4844 40.8961 34.4834C41.2098 34.4826 41.4782 34.4253 41.7014 34.311C41.9239 34.1966 42.1301 34.02 42.3201 33.7829L43.8482 34.9739C43.6967 35.1639 43.5232 35.3372 43.3292 35.4939C43.1345 35.6512 42.9114 35.7919 42.6599 35.916C42.4085 36.04 42.1189 36.1353 41.7913 36.203C41.4637 36.2694 41.0954 36.3044 40.6871 36.3055C40.1932 36.3068 39.7293 36.2315 39.2974 36.0809C38.8643 35.931 38.4863 35.7064 38.1623 35.4083C37.8382 35.111 37.5809 34.7369 37.3897 34.2868C37.1986 33.8368 37.1025 33.3138 37.101 32.716C37.0995 32.1376 37.1884 31.6232 37.3681 31.1721C37.5473 30.7211 37.7936 30.3387 38.1057 30.0248C38.4185 29.711 38.7908 29.4729 39.2225 29.3105C39.6542 29.148 40.1222 29.0665 40.6258 29.0652C41.6613 29.0624 42.4864 29.3945 43.1012 30.0613C43.7161 30.7282 44.0298 31.7584 44.0431 33.1524L39.5242 33.1642L39.5243 33.1636ZM41.7013 31.8358C41.672 31.4656 41.5593 31.1748 41.3642 30.9619C41.1685 30.749 40.9331 30.6429 40.6575 30.6436C40.3722 30.6443 40.1233 30.7543 39.9101 30.9727C39.6969 31.1912 39.5714 31.4814 39.535 31.8414L41.7013 31.8358ZM44.9622 30.9448L44.97 33.9888C44.971 34.3398 45.0072 34.6572 45.0794 34.9411C45.151 35.2256 45.2734 35.467 45.4445 35.6658C45.6164 35.8646 45.8421 36.0176 46.1225 36.1261C46.4029 36.2347 46.7476 36.2885 47.1559 36.2874C47.6408 36.2861 48.0657 36.2278 48.4312 36.1131C48.796 35.9984 49.1356 35.8316 49.4487 35.6123L48.8034 34.1068C48.68 34.2015 48.5405 34.2874 48.3835 34.3637C48.2266 34.4405 48.0631 34.4789 47.8925 34.4793C47.7122 34.4798 47.5954 34.4139 47.5429 34.281C47.4904 34.1488 47.4635 33.9637 47.4628 33.7265L47.4556 30.9383L49.1287 30.934L48.5085 29.2008L47.4511 29.2036L47.4466 27.4534L44.9525 27.4599L44.957 29.2101L43.9736 29.2126L43.9781 30.9474L44.9622 30.9448ZM53.3842 36.1292L54.4573 32.4852L55.564 36.1234L57.4739 36.1185L59.9222 29.1712L57.3711 29.1778L56.3548 32.8646L55.2761 29.1832L53.6514 29.1875L52.5926 32.8744L50.796 27.4422L48.3855 27.4484L51.489 36.1341L53.3842 36.1292ZM63.8517 36.102L63.6504 35.5618C63.4605 35.7424 63.2047 35.8974 62.8823 36.0268C62.559 36.1554 62.1843 36.22 61.757 36.2212C61.4331 36.222 61.134 36.1751 60.8583 36.0814C60.5826 35.9877 60.3416 35.8482 60.1379 35.663C59.9325 35.479 59.7731 35.259 59.659 35.0028C59.5436 34.7473 59.4863 34.4537 59.4855 34.1215C59.4843 33.7807 59.548 33.4811 59.6754 33.2249C59.8027 32.9687 59.9782 32.7504 60.2014 32.5693C60.4238 32.3888 60.6829 32.2409 60.9769 32.1264C61.2708 32.0118 61.5843 31.93 61.917 31.8822C62.2204 31.8339 62.5034 31.7978 62.7646 31.7732C63.0255 31.7495 63.266 31.7322 63.4841 31.722L63.4835 31.5516C63.4829 31.2675 63.3919 31.0588 63.2107 30.9269C63.0303 30.7944 62.8162 30.7287 62.5696 30.7294C62.2936 30.7302 62.0344 30.7828 61.7936 30.8883C61.5506 30.9929 61.3163 31.1356 61.0888 31.3169L59.9748 30.0825C60.3528 29.7491 60.7779 29.4877 61.2478 29.297C61.7172 29.1061 62.2797 29.0095 62.9357 29.0079C63.8474 29.0054 64.5743 29.224 65.1175 29.6634C65.6605 30.1037 65.9324 30.788 65.9353 31.7175L65.9465 36.0978L63.8513 36.1032L63.8517 36.102ZM63.4863 32.9598C63.3444 32.9699 63.202 32.9818 63.0594 32.9964C62.9162 33.0108 62.7696 33.0325 62.6169 33.0618C62.3228 33.1101 62.1137 33.2122 61.9908 33.3686C61.8676 33.5258 61.8072 33.6989 61.8075 33.8885C61.808 34.0877 61.8772 34.2507 62.0148 34.3784C62.1531 34.5059 62.3457 34.5697 62.5929 34.569C62.7736 34.5686 62.9377 34.5399 63.0842 34.4823C63.2316 34.4253 63.3665 34.3491 63.4899 34.2543L63.4863 32.9598ZM65.9632 38.5434C66.2389 38.6654 66.529 38.7642 66.8331 38.8393C67.1371 38.9143 67.4699 38.9515 67.832 38.9506C68.4019 38.9491 68.8972 38.8288 69.3195 38.5905C69.7418 38.3524 70.0756 37.944 70.3211 37.3648L73.7772 29.1345L71.2117 29.1412L69.8255 33.1986L68.404 29.1485L65.6965 29.1555L68.664 35.7899L68.2676 36.6869C68.1918 36.858 68.0998 36.9773 67.9906 37.0438C67.8814 37.1101 67.7367 37.1439 67.5565 37.1445C67.4046 37.1449 67.2386 37.1171 67.0574 37.0603C66.8768 37.0036 66.7204 36.9378 66.5866 36.8622L65.9632 38.5434Z" fill="url(#paint24_linear_556_24549)"/>
</g>
<defs>
<filter id="filter0_i_556_24549" x="67.313" y="52.0112" width="16.5405" height="16.5471" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="7.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_556_24549"/>
</filter>
<filter id="filter1_i_556_24549" x="1.31396" y="0.626221" width="92.5498" height="50.2874" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_556_24549"/>
</filter>
<filter id="filter2_i_556_24549" x="90.5039" y="19.7573" width="14.708" height="31.5645" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="1"/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_556_24549"/>
</filter>
<filter id="filter3_d_556_24549" x="82.4956" y="14.7651" width="22.8364" height="40.5479" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-5"/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_556_24549"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_556_24549" result="shape"/>
</filter>
<filter id="filter4_i_556_24549" x="4.53955" y="24.8806" width="20.4824" height="5.8584" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_556_24549"/>
</filter>
<filter id="filter5_i_556_24549" x="4.55566" y="23.6331" width="20.0146" height="4.33887" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_556_24549"/>
</filter>
<filter id="filter6_d_556_24549" x="87.5039" y="15.9048" width="8.98096" height="38.4163" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_556_24549"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_556_24549" result="shape"/>
</filter>
<filter id="filter7_i_556_24549" x="60.0122" y="12.137" width="38.5649" height="6.55273" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_556_24549"/>
</filter>
<filter id="filter8_i_556_24549" x="52.3994" y="39.532" width="42.0562" height="16.1943" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.75 0 0 0 0 0.75 0 0 0 0 0.75 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_556_24549"/>
</filter>
<filter id="filter9_i_556_24549" x="66.792" y="14.2346" width="18.1401" height="35.0276" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_556_24549"/>
</filter>
<linearGradient id="paint0_linear_556_24549" x1="106.356" y1="32.6354" x2="106.54" y2="37.2204" gradientUnits="userSpaceOnUse">
<stop stop-color="#3A2007"/>
<stop offset="0.25" stop-color="#583009"/>
<stop offset="0.5" stop-color="#6D3C0D"/>
<stop offset="0.75" stop-color="#583009"/>
<stop offset="1" stop-color="#3A2007"/>
</linearGradient>
<radialGradient id="paint1_radial_556_24549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(75.5835 60.2847) rotate(106.851) scale(8.27168 8.26768)">
<stop stop-color="#462918"/>
<stop offset="1" stop-color="#311C0F"/>
</radialGradient>
<linearGradient id="paint2_linear_556_24549" x1="48.9707" y1="19.4714" x2="42.1483" y2="41.9957" gradientUnits="userSpaceOnUse">
<stop stop-color="#191A19"/>
<stop offset="0.0946589" stop-color="#191A19" stop-opacity="0.940838"/>
<stop offset="0.176697" stop-color="#191A19" stop-opacity="0.889565"/>
<stop offset="1" stop-color="#191A19"/>
</linearGradient>
<linearGradient id="paint3_linear_556_24549" x1="48.9707" y1="19.4714" x2="47.8796" y2="44.479" gradientUnits="userSpaceOnUse">
<stop stop-color="#191A19"/>
<stop offset="0.298254" stop-color="#252525"/>
<stop offset="0.508641" stop-color="#2C2C2C"/>
<stop offset="1" stop-color="#191A19"/>
</linearGradient>
<linearGradient id="paint4_linear_556_24549" x1="98.7716" y1="19.8178" x2="99.7501" y2="50.1648" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F0F0F"/>
<stop offset="0.286458" stop-color="#282828"/>
<stop offset="0.5" stop-color="#3A3A3A"/>
<stop offset="0.677083" stop-color="#252525"/>
<stop offset="1" stop-color="#0F0F0F"/>
</linearGradient>
<linearGradient id="paint5_linear_556_24549" x1="92.4019" y1="20.2217" x2="104.909" y2="35.8766" gradientUnits="userSpaceOnUse">
<stop stop-color="#090909"/>
<stop offset="0.342202" stop-color="#0B0B0B" stop-opacity="0.9"/>
<stop offset="0.541287" stop-color="#0C0C0C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint6_linear_556_24549" x1="91.6953" y1="41.2682" x2="95.448" y2="40.6539" gradientUnits="userSpaceOnUse">
<stop stop-color="#090909"/>
<stop offset="0.416537" stop-color="#0B0B0B"/>
<stop offset="1" stop-color="#0C0C0C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint7_linear_556_24549" x1="59.9847" y1="9.12382" x2="98.379" y2="9.18096" gradientUnits="userSpaceOnUse">
<stop offset="0.0436342" stop-color="#131313"/>
<stop offset="0.625" stop-color="#0D0D0D"/>
<stop offset="0.852691" stop-color="#1E1E1E"/>
<stop offset="0.891116" stop-color="#2F2F2F"/>
<stop offset="0.941341" stop-color="#505050"/>
<stop offset="0.975426" stop-color="#6C6C6C"/>
<stop offset="0.99194" stop-color="#989898"/>
<stop offset="1" stop-color="#8A8A8A"/>
</linearGradient>
<radialGradient id="paint8_radial_556_24549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(75.5837 60.2848) rotate(106.851) scale(4.83365 4.83132)">
<stop stop-color="#C6884D"/>
<stop offset="1" stop-color="#62360C"/>
</radialGradient>
<linearGradient id="paint9_linear_556_24549" x1="94.6667" y1="57.7708" x2="51.8541" y2="58.9698" gradientUnits="userSpaceOnUse">
<stop stop-color="#5C5D5C"/>
<stop offset="0.00716204" stop-color="#3A3A3A"/>
<stop offset="0.0872404" stop-color="#202020"/>
<stop offset="0.948425" stop-color="#191919"/>
<stop offset="0.976051" stop-color="#323333"/>
<stop offset="1" stop-color="#D9D9D9"/>
</linearGradient>
<radialGradient id="paint10_radial_556_24549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(23.3867 20.3606) rotate(176.649) scale(8.24222 153.083)">
<stop stop-opacity="0.6"/>
<stop offset="0.840522" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint11_linear_556_24549" x1="24.8229" y1="28.1906" x2="26.1463" y2="27.9385" gradientUnits="userSpaceOnUse">
<stop stop-color="#151515"/>
<stop offset="1" stop-color="#1B1B1B" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint12_linear_556_24549" x1="25.0176" y1="28.3585" x2="4.61463" y2="29.5139" gradientUnits="userSpaceOnUse">
<stop stop-color="#131313" stop-opacity="0.01"/>
<stop offset="0.171962" stop-color="#131313"/>
<stop offset="0.246073" stop-color="#131313"/>
<stop offset="0.324566" stop-color="#1C1C1C"/>
<stop offset="0.891281" stop-color="#2E2E2E"/>
<stop offset="0.920878" stop-color="#363636"/>
<stop offset="0.943501" stop-color="#3F3F3F"/>
<stop offset="0.962891" stop-color="#4B4B4B"/>
<stop offset="0.982668" stop-color="#686868"/>
<stop offset="0.995725" stop-color="#8D8D8D"/>
<stop offset="1" stop-color="#808080"/>
</linearGradient>
<linearGradient id="paint13_linear_556_24549" x1="13.8296" y1="26.1175" x2="13.802" y2="28.2844" gradientUnits="userSpaceOnUse">
<stop stop-color="#151515"/>
<stop offset="1" stop-color="#232323"/>
</linearGradient>
<linearGradient id="paint14_linear_556_24549" x1="25.6523" y1="29.1631" x2="20.6652" y2="29.7565" gradientUnits="userSpaceOnUse">
<stop offset="0.0827287" stop-color="#020201" stop-opacity="0"/>
<stop offset="0.128184" stop-color="#040404"/>
<stop offset="0.405052" stop-color="#040404"/>
<stop offset="0.627745" stop-color="#0B0B0B"/>
<stop offset="0.791086" stop-color="#0E0E0E" stop-opacity="0.646353"/>
<stop offset="1" stop-color="#131313" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint15_radial_556_24549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(76.576 24.3352) rotate(91.8439) scale(4.66384 15.7841)">
<stop offset="0.482277" stop-color="#010101"/>
<stop offset="1" stop-color="#020202" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint16_radial_556_24549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(93.8111 35.6571) rotate(-179.386) scale(2.74457 17.6472)">
<stop offset="0.482277" stop-color="#010101"/>
<stop offset="1" stop-color="#020202" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint17_linear_556_24549" x1="92.4913" y1="19.9869" x2="94.6853" y2="20.073" gradientUnits="userSpaceOnUse">
<stop offset="0.00520833" stop-color="#1E1E1E" stop-opacity="0"/>
<stop offset="0.432292" stop-color="#646564"/>
<stop offset="1" stop-color="#1B1B1B" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint18_linear_556_24549" x1="80.2816" y1="16.0141" x2="80.2894" y2="12.0049" gradientUnits="userSpaceOnUse">
<stop stop-color="#2B2A2A"/>
<stop offset="0.828125" stop-color="#151515"/>
</linearGradient>
<radialGradient id="paint19_radial_556_24549" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(80.404 14.5023) rotate(90.968) scale(1.35047 14.2008)">
<stop offset="0.482277" stop-color="#010101"/>
<stop offset="1" stop-color="#020202" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint20_linear_556_24549" x1="72.6694" y1="43.6962" x2="72.8559" y2="56.462" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="0.87252" stop-color="#2A2A2A"/>
<stop offset="1" stop-color="#2E2F2E"/>
</linearGradient>
<linearGradient id="paint21_linear_556_24549" x1="93.0704" y1="52.3621" x2="58.0429" y2="45.1949" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="0.553397" stop-opacity="0.5"/>
<stop offset="1" stop-color="#2E2F2E" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint22_linear_556_24549" x1="74.6965" y1="34.264" x2="74.9029" y2="43.9077" gradientUnits="userSpaceOnUse">
<stop offset="0.0771464" stop-color="#050505" stop-opacity="0"/>
<stop offset="1" stop-color="#030303"/>
</linearGradient>
<linearGradient id="paint23_linear_556_24549" x1="81.8229" y1="15.1926" x2="72.6979" y2="51.097" gradientUnits="userSpaceOnUse">
<stop stop-color="#111111"/>
<stop offset="0.53125" stop-color="#141414"/>
<stop offset="1" stop-color="#0B0B0B"/>
</linearGradient>
<linearGradient id="paint24_linear_556_24549" x1="51.7647" y1="26.6458" x2="51.7968" y2="38.9922" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.37"/>
</linearGradient>
<clipPath id="clip0_556_24549">
<rect width="111" height="70" fill="white"/>
</clipPath>
</defs>
</svg>
