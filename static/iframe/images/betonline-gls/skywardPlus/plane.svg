<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g transform="translate(47, 172) scale(0.43) rotate(-18)">
<g clip-path="url(#clip0_479_21951)">
<path d="M781.915 345.383L822.92 344.447C826.772 344.36 829.966 347.415 830.054 351.272L830.474 369.845C830.562 373.702 827.514 376.901 823.661 376.989L782.657 377.924C778.809 378.013 775.615 374.957 775.527 371.101L775.103 352.527C775.015 348.67 778.067 345.472 781.915 345.383Z" fill="url(#paint0_linear_479_21951)"/>
<g filter="url(#filter0_di_479_21951)">
<path d="M552.305 613.339C519.345 603.342 500.717 568.493 510.698 535.498C520.678 502.505 555.486 483.863 588.447 493.859C621.403 503.855 640.031 538.704 630.05 571.697C620.07 604.692 585.262 623.334 552.305 613.339Z" fill="url(#paint1_radial_479_21951)"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M702.411 250.7L708.218 467.775C708.218 467.775 511.328 457.381 395.978 440.272C296.795 425.561 280.126 422.355 166.239 381.302C163.541 388.996 154.363 411.97 140.035 414.925C130.369 416.919 121.183 418.288 115.16 410.462C111.199 405.316 114.581 400.616 118.268 395.493C121.369 391.184 124.685 386.576 124.029 381.152C123.153 373.901 117.291 363.709 117.291 363.709L119.881 364.494C118.584 364.022 117.276 363.547 115.958 363.067C58.2978 342.092 18.7864 325.92 12.6291 298.428C6.47197 270.936 13.9476 203.324 20.928 182.971C29.4087 158.245 65.6931 89.2341 120.641 105.892C208.595 132.557 247.004 251.51 247.004 251.51L284.532 250.17L702.411 250.7Z" fill="url(#paint2_linear_479_21951)"/>
<g filter="url(#filter1_i_479_21951)">
<path d="M708.26 467.8L702.406 250.783L284.521 250.165L246.992 251.497C246.992 251.497 208.558 132.568 120.597 105.892C65.6446 89.2265 29.3742 158.211 20.8986 182.929C13.9224 203.274 6.4609 270.867 12.6239 298.352C18.7872 325.838 58.3026 342.014 115.968 362.995C277.196 421.658 283.801 423.577 396.008 440.239C511.364 457.368 708.26 467.8 708.26 467.8Z" fill="url(#paint3_linear_479_21951)"/>
<path d="M433.804 303.047L441.401 303.05L441.415 347.123L464.501 347.131L464.504 353.924L433.821 353.914L433.804 303.047Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M367.654 305.065C370.859 306.365 373.649 308.138 375.965 310.443C378.28 312.749 380.061 315.466 381.368 318.538C382.674 321.67 383.328 325.038 383.329 328.642C383.33 332.245 382.679 335.672 381.374 338.743C380.07 341.814 378.29 344.531 375.977 346.834C373.663 349.079 370.875 350.909 367.67 352.208C364.407 353.506 360.846 354.155 357.048 354.154C353.191 354.152 349.63 353.502 346.425 352.201C343.22 350.9 340.43 349.127 338.115 346.822C335.859 344.576 334.019 341.799 332.712 338.727C331.405 335.595 330.751 332.228 330.75 328.624C330.749 325.021 331.401 321.594 332.705 318.522C334.01 315.451 335.789 312.734 338.103 310.431C340.417 308.187 343.205 306.356 346.409 305.057C349.673 303.759 353.233 303.111 357.031 303.111C360.889 303.113 364.45 303.764 367.654 305.065ZM374.133 335.905C375.023 333.66 375.437 331.179 375.437 328.639C375.436 326.098 374.96 323.617 374.129 321.372C373.297 319.126 372.05 317.118 370.447 315.463C368.844 313.808 366.945 312.449 364.69 311.444C362.493 310.439 359.941 309.965 357.152 309.964C354.363 309.964 351.871 310.435 349.616 311.439C347.421 312.443 345.462 313.801 343.861 315.454C342.318 317.108 341.073 319.116 340.184 321.361C339.294 323.605 338.879 326.086 338.88 328.627C338.881 331.167 339.297 333.648 340.188 335.893C341.02 338.139 342.267 340.148 343.87 341.802C345.473 343.457 347.372 344.816 349.627 345.822C351.824 346.826 354.375 347.3 357.165 347.301C359.954 347.302 362.446 346.83 364.701 345.827C366.896 344.823 368.855 343.465 370.456 341.811C371.999 340.157 373.244 338.149 374.133 335.905Z" fill="white"/>
<path d="M476.59 303.05L468.992 303.048L469.009 353.98L476.607 353.982L476.59 303.05Z" fill="white"/>
<path d="M534.683 331.702L534.688 347.121L561.573 347.131L561.575 353.925L527.094 353.913L527.077 303.046L560.252 303.057L560.254 309.91L534.676 309.902L534.681 324.848L558.538 324.856L558.541 331.71L534.683 331.702Z" fill="white"/>
<path d="M422.134 336.849L387.88 303.045L387.897 353.911L395.019 353.914L395.007 320.535L429.321 353.925L429.304 303.059L422.123 303.056L422.134 336.849Z" fill="white"/>
<path d="M481.093 303.045L515.406 337.086L515.395 303.056L522.517 303.059L522.533 353.689L488.279 320.771L488.29 353.914L481.11 353.912L481.093 303.045Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M248.586 339.218L248.587 339.513C248.587 339.632 248.586 339.75 248.646 339.809L248.646 340.045C248.587 342.645 247.936 344.83 246.69 346.602C245.385 348.374 243.724 349.85 241.706 350.913C239.689 352.035 237.375 352.801 234.823 353.273C232.272 353.745 229.72 353.98 227.169 353.979L203.432 353.971L203.422 324.671L203.303 324.671C203.303 324.671 185.382 323.484 181.168 323.482C181.168 323.482 193.155 322.482 203.303 322.308L203.421 322.308L203.355 302.991L224.778 302.998C227.27 302.999 229.763 303.177 232.255 303.473C234.748 303.828 237.062 304.42 239.08 305.365C241.098 306.312 242.82 307.671 244.066 309.384C245.373 311.098 246.026 313.343 246.027 316.179C246.028 319.073 245.139 321.495 243.299 323.384L242.825 323.857C242.825 323.857 242.647 324.093 242.232 324.448C241.757 324.802 241.283 325.156 240.571 325.569C240.511 325.599 240.467 325.629 240.422 325.658C240.378 325.687 240.333 325.717 240.274 325.746C236.535 324.091 232.321 323.145 229.235 322.612C231.075 321.845 231.846 321.077 232.083 320.782C232.17 320.695 232.225 320.609 232.295 320.499C232.32 320.459 232.347 320.416 232.379 320.368C232.795 319.659 233.032 318.833 233.032 317.887C233.031 316.883 232.794 316.115 232.319 315.465C231.784 314.815 231.191 314.342 230.419 313.987C229.588 313.574 228.698 313.337 227.689 313.16C226.68 312.982 225.672 312.923 224.722 312.922L216.236 312.919L216.299 322.666L216.714 322.667C230.482 323.498 239.859 325.983 244.548 330.179C245.023 330.533 247.516 332.661 248.348 336.678L248.467 337.092L248.467 337.269C248.467 337.357 248.482 337.446 248.497 337.534C248.512 337.623 248.527 337.712 248.527 337.801L248.526 338.037C248.527 338.244 248.541 338.436 248.556 338.627C248.571 338.819 248.586 339.012 248.586 339.218ZM234.998 340.927L235.057 340.808C235.472 340.041 236.48 337.501 233.987 334.369C232.384 332.538 228.585 329.937 220.098 327.689L214.816 326.447L215.529 327.274L215.647 327.392C215.663 327.423 215.69 327.462 215.725 327.511C215.826 327.653 215.991 327.885 216.123 328.278L216.301 328.81C216.305 328.855 216.311 328.899 216.316 328.943C216.37 329.422 216.42 329.864 216.42 330.406C216.361 332.001 216.304 339.68 216.305 343.047L216.306 344.11L216.781 344.17L227.343 344.173C228.293 344.173 229.302 344.056 230.311 343.879C231.319 343.702 232.209 343.348 232.981 342.876C233.752 342.403 234.404 341.813 234.938 341.045C234.938 341.045 234.997 340.986 234.998 340.927Z" fill="#EE3536"/>
<path d="M252.914 353.972L252.897 303.046L287.081 303.057L287.084 313.396L264.71 313.388L264.713 323.018L285.901 323.025L285.904 332.892L264.776 332.884L264.78 343.46L288.4 343.468L288.404 353.984L252.914 353.972Z" fill="#EE3536"/>
<path d="M334.374 308.081C336.45 306.072 338.823 304.419 341.494 303.062L291.583 303.045L291.586 313.561L305.948 313.566L305.962 353.976L318.247 353.98L318.233 313.57L329.984 313.574C331.23 311.565 332.713 309.734 334.374 308.081Z" fill="#EE3536"/>
<path d="M744.609 430.813L736.634 285.758C735.558 266.18 718.523 250.613 698.209 250.641L296.621 251.204L285.624 251.611C268.876 252.232 253.426 242.076 246.089 227.364C227.761 190.615 186.708 124.78 120.843 105.854C62.7834 89.171 26.645 159.009 18.435 184.02C11.6773 204.606 5.70076 272.952 12.9117 300.717C20.1227 328.482 61.9874 344.716 123.01 365.754C293.624 424.574 300.6 426.494 418.68 443.003C506.823 455.327 639.931 463.955 706.239 467.757C728.112 469.009 745.77 451.903 744.609 430.813Z" fill="url(#paint4_linear_479_21951)"/>
</g>
<g filter="url(#filter2_i_479_21951)">
<path d="M790.714 289.743L793.804 433.539C794.315 457.2 775.474 476.857 751.813 477.357L702.87 478.389L697.931 248.77L746.875 247.738C770.536 247.238 790.202 266.009 790.712 289.67L790.714 289.743Z" fill="url(#paint5_linear_479_21951)"/>
</g>
<path d="M697.901 248.888L746.162 247.792C770.64 247.235 790.933 266.645 791.491 291.146L794.818 437.77C795.371 462.269 775.981 482.582 751.503 483.139L703.242 484.235L697.901 248.888Z" fill="url(#paint6_linear_479_21951)" fill-opacity="0.2"/>
<g filter="url(#filter3_d_479_21951)">
<path d="M697.869 248.886L746.138 247.8C770.62 247.249 790.922 266.662 791.484 291.161L794.704 431.88C795.267 456.379 775.877 476.687 751.395 477.239L703.126 478.325L697.869 248.886Z" fill="url(#paint7_linear_479_21951)" fill-opacity="0.01"/>
</g>
<g filter="url(#filter4_d_479_21951)">
<path d="M456.319 194.717L740.14 193.776C742.101 193.77 743.682 192.175 743.673 190.215L743.552 157.322C743.543 155.361 741.951 153.778 739.989 153.784L456.17 154.725C454.211 154.732 452.629 156.326 452.636 158.286L452.759 191.18C452.766 193.139 454.36 194.723 456.319 194.717Z" fill="url(#paint8_linear_479_21951)"/>
</g>
<path d="M535.508 543.027C529.669 562.323 540.543 582.673 559.81 588.514C579.077 594.355 599.401 583.464 605.24 564.168C611.079 544.872 600.209 524.524 580.942 518.683C561.675 512.842 541.347 523.732 535.508 543.027Z" fill="url(#paint9_radial_479_21951)" fill-opacity="0.5"/>
<path d="M395.016 514.259L407.202 517.953C419.112 521.564 700.954 508.038 706.946 507.066C712.386 506.185 712.457 502.142 712.422 501.399L712.416 501.282C712.416 501.282 712.417 501.323 712.422 501.399L715.049 539.956C715.049 539.956 714.207 545.877 707.453 546.212C700.7 546.548 422.293 560.296 410.056 558.761C397.813 557.229 396.131 554.539 396.131 554.539L395.016 514.259Z" fill="url(#paint10_linear_479_21951)"/>
<path d="M240.544 236.727L109.648 257.641L246.598 261.305L240.544 236.727Z" fill="url(#paint11_radial_479_21951)"/>
<path d="M205.163 314.663L203.995 306.791L185.906 301.307L189.1 320.64L205.163 314.663Z" fill="url(#paint12_linear_479_21951)"/>
<g filter="url(#filter5_i_479_21951)">
<path d="M34.8997 307.681C34.9022 307.434 35.0654 307.236 35.3124 307.242C42.2953 307.408 136.752 309.645 143.143 309.054C149.348 308.48 181.382 302.31 185.517 301.511C185.762 301.464 185.991 301.629 186.033 301.875L189.108 320.23C189.147 320.463 189.017 320.681 188.787 320.732C184.433 321.693 145.123 330.355 142.657 330.532C135.379 331.054 41.9205 328.639 35.0575 328.46C34.8153 328.454 34.6624 328.255 34.6649 328.013L34.8997 307.681Z" fill="url(#paint13_linear_479_21951)"/>
</g>
<g filter="url(#filter6_i_479_21951)">
<path d="M78.7678 299.61L185.707 301.531L142.867 309.725L34.7843 307.284L78.7678 299.61Z" fill="url(#paint14_linear_479_21951)"/>
</g>
<path d="M156.605 307.017L185.945 301.369L189.201 320.659L158.029 327.237L156.605 307.017Z" fill="url(#paint15_linear_479_21951)" fill-opacity="0.5"/>
<path d="M576.727 317.453C639.396 319.472 690.702 305.358 691.325 285.926C691.952 266.497 641.656 249.108 578.991 247.089C516.323 245.069 465.016 259.184 464.394 278.615C463.767 298.045 514.063 315.434 576.727 317.453Z" fill="url(#paint16_radial_479_21951)" fill-opacity="0.7"/>
<path d="M687.127 367.5C686.473 428.628 695.205 478.28 706.637 478.404C718.065 478.526 727.861 429.072 728.516 367.944C729.171 306.817 720.438 257.164 709.006 257.041C697.578 256.919 687.782 306.373 687.127 367.5Z" fill="url(#paint17_radial_479_21951)" fill-opacity="0.6"/>
<g opacity="0.5" filter="url(#filter7_d_479_21951)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M703.129 478.399L697.938 248.882L700.2 248.833L705.387 478.348L703.129 478.399Z" fill="url(#paint18_linear_479_21951)"/>
</g>
<g filter="url(#filter8_i_479_21951)">
<path d="M660.951 224.573L506.872 220.43L455.791 194.89L452.956 192.073L743.758 190.238L742.077 193.221L660.951 224.573Z" fill="url(#paint19_linear_479_21951)"/>
</g>
<path d="M606.553 218.251C655.69 219.08 695.605 215.192 695.701 209.564C695.797 203.936 656.035 198.699 606.894 197.867C557.757 197.038 517.841 200.926 517.745 206.554C517.65 212.182 557.411 217.419 606.553 218.251Z" fill="url(#paint20_radial_479_21951)" fill-opacity="0.6"/>
<g filter="url(#filter9_i_479_21951)">
<path d="M395.719 514.114L456.25 427.524C456.415 427.285 456.689 427.143 456.98 427.145L692.067 428.426C692.465 428.427 692.814 428.698 692.919 429.087L712.665 503.832C712.723 504.053 712.694 504.287 712.583 504.486L709.607 509.899C709.46 510.168 709.181 510.341 708.875 510.357L561.337 518.065L408.468 519.153C408.375 519.153 408.287 519.14 408.202 519.114L396.189 515.471C395.612 515.296 395.371 514.606 395.719 514.114Z" fill="url(#paint21_linear_479_21951)"/>
</g>
<path d="M395.719 514.157L456.265 427.536C456.43 427.297 456.704 427.156 456.996 427.157L692.067 428.388C692.469 428.39 692.818 428.661 692.923 429.05L712.476 503.331C712.536 503.562 712.504 503.808 712.377 504.012L710.714 506.796C710.565 507.045 710.308 507.206 710.019 507.226L561.329 518.073L408.466 519.194C408.374 519.194 408.285 519.181 408.2 519.155L396.188 515.514C395.611 515.339 395.371 514.649 395.719 514.157Z" fill="url(#paint22_linear_479_21951)"/>
<path d="M482.388 340.63L675.906 344.594L692.712 428.704L456.057 427.642L482.388 340.63Z" fill="url(#paint23_linear_479_21951)" fill-opacity="0.8"/>
<g filter="url(#filter10_i_479_21951)">
<path d="M631.552 468.653L640.847 210.523L572.395 206.036C572.395 206.036 590.319 280.89 581.8 329.607C571.042 391.147 504.083 470.383 504.083 470.383L631.552 468.653Z" fill="url(#paint24_linear_479_21951)"/>
</g>
<path d="M153.679 221.947L153.696 221.447C153.72 220.793 153.692 220.14 153.642 219.441L153.609 219.107L153.615 218.964C153.592 218.68 153.552 218.366 153.509 218.067L153.469 217.768L153.454 217.768L153.321 217.01C152.11 210.182 148.098 206.404 147.281 205.705C139.578 198.347 123.907 193.504 100.732 191.311L100.03 191.244L100.554 174.727L114.905 175.236C116.589 175.302 118.264 175.503 119.916 175.836C121.489 176.138 123.014 176.652 124.447 177.366C125.684 177.981 126.745 178.9 127.531 180.036C128.287 181.134 128.648 182.531 128.599 184.191C128.586 185.67 128.153 187.116 127.347 188.356C127.172 188.61 126.982 188.854 126.78 189.086C126.368 189.539 124.98 190.836 121.806 192.069C126.952 193.145 134.095 195.032 140.265 198.017L140.747 197.778L140.765 197.778L140.801 197.759C141.828 197.26 142.791 196.637 143.667 195.904C144.04 195.616 144.392 195.303 144.721 194.965L145.531 194.193C148.753 191.089 150.45 187.025 150.576 182.117C150.72 177.359 149.753 173.413 147.716 170.426C145.625 167.414 142.805 164.981 139.521 163.353C135.921 161.576 132.068 160.369 128.098 159.775C123.922 159.106 119.708 158.693 115.481 158.539L79.3151 157.257L78.2467 190.082L78.0775 190.076C60.8682 189.764 40.5965 190.728 40.5965 190.728C47.728 191.016 77.9494 194.032 77.9494 194.032L78.1189 194.037L76.3611 243.76L116.53 245.184C120.869 245.33 125.212 245.077 129.504 244.429C133.6 243.853 137.58 242.641 141.302 240.837C144.702 239.189 147.675 236.777 149.99 233.791C152.208 230.892 153.442 227.199 153.652 222.777L153.665 222.411C153.686 222.253 153.681 222.114 153.679 221.947ZM130.826 223.257L130.726 223.429C130.683 223.506 130.636 223.581 130.583 223.653C129.68 224.887 128.522 225.911 127.187 226.656C125.751 227.443 124.185 227.961 122.565 228.186C120.895 228.442 119.205 228.545 117.516 228.492L99.6838 227.861L98.9238 227.732L98.9859 225.972C99.2313 220.291 99.7925 207.2 99.9271 204.486C99.9818 203.589 99.9327 202.688 99.7808 201.801L99.7256 201.523L99.5653 200.922C99.4169 200.37 99.1588 199.855 98.8065 199.406C98.7555 199.333 98.7047 199.262 98.6503 199.192L97.5264 197.729L106.438 200.123C120.693 204.454 126.86 209.126 129.55 212.321C133.43 217.722 131.515 222.046 130.826 223.257Z" fill="white"/>
</g>
    </g>
<defs>
<filter id="filter0_di_479_21951" x="504.005" y="491.161" width="132.738" height="132.876" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_479_21951"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_479_21951" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="7.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_479_21951"/>
</filter>
<filter id="filter1_i_479_21951" x="9.63379" y="103.308" width="735.03" height="368.513" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="15"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_479_21951"/>
</filter>
<filter id="filter2_i_479_21951" x="687.931" y="247.728" width="105.883" height="231.661" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-10" dy="1"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_479_21951"/>
</filter>
<filter id="filter3_d_479_21951" x="682.869" y="237.788" width="116.848" height="250.536" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-5"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_479_21951"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_479_21951" result="shape"/>
</filter>
<filter id="filter4_d_479_21951" x="448.636" y="149.784" width="299.036" height="48.9335" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_479_21951"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_479_21951" result="shape"/>
</filter>
<filter id="filter5_i_479_21951" x="34.665" y="299.503" width="154.45" height="31.1026" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_479_21951"/>
</filter>
<filter id="filter6_i_479_21951" x="34.7842" y="296.61" width="150.923" height="13.1151" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_479_21951"/>
</filter>
<filter id="filter7_d_479_21951" x="692.938" y="244.833" width="15.4492" height="237.565" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_479_21951"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_479_21951" result="shape"/>
</filter>
<filter id="filter8_i_479_21951" x="452.957" y="190.238" width="290.801" height="39.3348" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_479_21951"/>
</filter>
<filter id="filter9_i_479_21951" x="395.557" y="423.145" width="317.137" height="96.008" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.75 0 0 0 0 0.75 0 0 0 0 0.75 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_479_21951"/>
</filter>
<filter id="filter10_i_479_21951" x="504.083" y="206.036" width="136.764" height="264.347" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0 0.74902 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_479_21951"/>
</filter>
<linearGradient id="paint0_linear_479_21951" x1="802.42" y1="344.916" x2="803.804" y2="379.521" gradientUnits="userSpaceOnUse">
<stop stop-color="#3A2007"/>
<stop offset="0.25" stop-color="#583009"/>
<stop offset="0.5" stop-color="#6D3C0D"/>
<stop offset="0.75" stop-color="#583009"/>
<stop offset="1" stop-color="#3A2007"/>
</linearGradient>
<radialGradient id="paint1_radial_479_21951" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(570.376 553.598) rotate(106.83) scale(62.412 62.3607)">
<stop stop-color="#462918"/>
<stop offset="1" stop-color="#311C0F"/>
</radialGradient>
<linearGradient id="paint2_linear_479_21951" x1="369.699" y1="245.562" x2="318.168" y2="415.537" gradientUnits="userSpaceOnUse">
<stop stop-color="#191A19"/>
<stop offset="0.0946589" stop-color="#191A19" stop-opacity="0.940838"/>
<stop offset="0.176697" stop-color="#191A19" stop-opacity="0.889565"/>
<stop offset="1" stop-color="#191A19"/>
</linearGradient>
<linearGradient id="paint3_linear_479_21951" x1="369.688" y1="245.576" x2="361.411" y2="434.262" gradientUnits="userSpaceOnUse">
<stop stop-color="#191A19"/>
<stop offset="0.298254" stop-color="#252525"/>
<stop offset="0.508641" stop-color="#2C2C2C"/>
<stop offset="1" stop-color="#191A19"/>
</linearGradient>
<linearGradient id="paint4_linear_479_21951" x1="323.66" y1="92.2787" x2="334.616" y2="459.935" gradientUnits="userSpaceOnUse">
<stop stop-color="#2D2E2C" stop-opacity="0"/>
<stop offset="0.305253" stop-color="#2D2E2C" stop-opacity="0.2"/>
<stop offset="0.484375" stop-color="#2D2E2C" stop-opacity="0"/>
<stop offset="0.695154" stop-color="#2D2E2C" stop-opacity="0.3"/>
<stop offset="1" stop-color="#020202" stop-opacity="0.8"/>
</linearGradient>
<linearGradient id="paint5_linear_479_21951" x1="745.201" y1="248.185" x2="752.543" y2="477.218" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F0F0F"/>
<stop offset="0.286458" stop-color="#282828"/>
<stop offset="0.5" stop-color="#3A3A3A"/>
<stop offset="0.677083" stop-color="#252525"/>
<stop offset="1" stop-color="#0F0F0F"/>
</linearGradient>
<linearGradient id="paint6_linear_479_21951" x1="697.194" y1="251.226" x2="761.771" y2="317.97" gradientUnits="userSpaceOnUse">
<stop stop-color="#090909"/>
<stop offset="0.342202" stop-color="#0B0B0B" stop-opacity="0.9"/>
<stop offset="0.541287" stop-color="#0C0C0C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint7_linear_479_21951" x1="691.866" y1="410.061" x2="720.167" y2="405.425" gradientUnits="userSpaceOnUse">
<stop stop-color="#090909"/>
<stop offset="0.416537" stop-color="#0B0B0B"/>
<stop offset="1" stop-color="#0C0C0C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint8_linear_479_21951" x1="452.745" y1="167.436" x2="742.266" y2="167.805" gradientUnits="userSpaceOnUse">
<stop offset="0.0436342" stop-color="#131313"/>
<stop offset="0.625" stop-color="#0D0D0D"/>
<stop offset="0.852691" stop-color="#1E1E1E"/>
<stop offset="0.891116" stop-color="#2F2F2F"/>
<stop offset="0.941341" stop-color="#505050"/>
<stop offset="0.975426" stop-color="#6C6C6C"/>
<stop offset="0.99194" stop-color="#989898"/>
<stop offset="1" stop-color="#8A8A8A"/>
</linearGradient>
<radialGradient id="paint9_radial_479_21951" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(570.376 553.598) rotate(106.837) scale(36.4789 36.4336)">
<stop stop-color="#C6884D"/>
<stop offset="1" stop-color="#62360C"/>
</radialGradient>
<linearGradient id="paint10_linear_479_21951" x1="714.272" y1="534.624" x2="391.442" y2="543.657" gradientUnits="userSpaceOnUse">
<stop stop-color="#5C5D5C"/>
<stop offset="0.00716204" stop-color="#3A3A3A"/>
<stop offset="0.0872404" stop-color="#202020"/>
<stop offset="0.948425" stop-color="#191919"/>
<stop offset="0.976051" stop-color="#323333"/>
<stop offset="1" stop-color="#D9D9D9"/>
</linearGradient>
<radialGradient id="paint11_radial_479_21951" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(176.783 252.275) rotate(176.646) scale(62.151 1155.38)">
<stop stop-opacity="0.6"/>
<stop offset="0.840522" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint12_linear_479_21951" x1="187.612" y1="311.369" x2="197.591" y2="309.47" gradientUnits="userSpaceOnUse">
<stop stop-color="#151515"/>
<stop offset="1" stop-color="#1B1B1B" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint13_linear_479_21951" x1="189.082" y1="312.654" x2="35.2321" y2="321.393" gradientUnits="userSpaceOnUse">
<stop stop-color="#131313" stop-opacity="0.01"/>
<stop offset="0.171962" stop-color="#131313"/>
<stop offset="0.246073" stop-color="#131313"/>
<stop offset="0.324566" stop-color="#1C1C1C"/>
<stop offset="0.891281" stop-color="#2E2E2E"/>
<stop offset="0.920878" stop-color="#363636"/>
<stop offset="0.943501" stop-color="#3F3F3F"/>
<stop offset="0.962891" stop-color="#4B4B4B"/>
<stop offset="0.982668" stop-color="#686868"/>
<stop offset="0.995725" stop-color="#8D8D8D"/>
<stop offset="1" stop-color="#808080"/>
</linearGradient>
<linearGradient id="paint14_linear_479_21951" x1="104.714" y1="295.726" x2="104.502" y2="312.075" gradientUnits="userSpaceOnUse">
<stop stop-color="#151515"/>
<stop offset="1" stop-color="#232323"/>
</linearGradient>
<linearGradient id="paint15_linear_479_21951" x1="193.865" y1="318.71" x2="156.259" y2="323.18" gradientUnits="userSpaceOnUse">
<stop offset="0.0827287" stop-color="#020201" stop-opacity="0"/>
<stop offset="0.128184" stop-color="#040404"/>
<stop offset="0.405052" stop-color="#040404"/>
<stop offset="0.627745" stop-color="#0B0B0B"/>
<stop offset="0.791086" stop-color="#0E0E0E" stop-opacity="0.646353"/>
<stop offset="1" stop-color="#131313" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint16_radial_479_21951" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(577.859 282.271) rotate(91.8422) scale(35.2 119.021)">
<stop offset="0.482277" stop-color="#010101"/>
<stop offset="1" stop-color="#020202" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint17_radial_479_21951" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(707.822 367.722) rotate(-179.385) scale(20.6955 133.192)">
<stop offset="0.482277" stop-color="#010101"/>
<stop offset="1" stop-color="#020202" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint18_linear_479_21951" x1="697.844" y1="249.453" x2="714.393" y2="250.098" gradientUnits="userSpaceOnUse">
<stop offset="0.00520833" stop-color="#1E1E1E" stop-opacity="0"/>
<stop offset="0.432292" stop-color="#646564"/>
<stop offset="1" stop-color="#1B1B1B" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint19_linear_479_21951" x1="605.805" y1="219.463" x2="605.87" y2="189.213" gradientUnits="userSpaceOnUse">
<stop stop-color="#2B2A2A"/>
<stop offset="0.828125" stop-color="#151515"/>
</linearGradient>
<radialGradient id="paint20_radial_479_21951" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(606.723 208.059) rotate(90.9671) scale(10.1926 107.082)">
<stop offset="0.482277" stop-color="#010101"/>
<stop offset="1" stop-color="#020202" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint21_linear_479_21951" x1="548.391" y1="428.404" x2="549.779" y2="524.734" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="0.87252" stop-color="#2A2A2A"/>
<stop offset="1" stop-color="#2E2F2E"/>
</linearGradient>
<linearGradient id="paint22_linear_479_21951" x1="702.237" y1="493.803" x2="438.091" y2="439.803" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="0.553397" stop-opacity="0.5"/>
<stop offset="1" stop-color="#2E2F2E" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint23_linear_479_21951" x1="563.687" y1="357.208" x2="565.246" y2="429.993" gradientUnits="userSpaceOnUse">
<stop offset="0.0771464" stop-color="#050505" stop-opacity="0"/>
<stop offset="1" stop-color="#030303"/>
</linearGradient>
<linearGradient id="paint24_linear_479_21951" x1="617.398" y1="213.275" x2="548.447" y2="484.193" gradientUnits="userSpaceOnUse">
<stop stop-color="#111111"/>
<stop offset="0.53125" stop-color="#141414"/>
<stop offset="1" stop-color="#0B0B0B"/>
</linearGradient>
<clipPath id="clip0_479_21951">
<rect width="837" height="623" fill="white" transform="translate(0.434082 0.486206)"/>
</clipPath>
</defs>
</svg>
