{"compileOnSave": false, "compilerOptions": {"typeRoots": ["./workspace/types", "./node_modules/@types", "./node_modules/@nx/react/typings"], "types": ["global.iframe", "timer", "cssmodule", "image", "node"], "rootDir": ".", "baseUrl": ".", "skipLibCheck": true, "skipDefaultLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "declaration": false, "forceConsistentCasingInFileNames": true, "importHelpers": true, "noEmitHelpers": true, "jsx": "react-jsx", "jsxImportSource": "react", "target": "es6", "useDefineForClassFields": true, "lib": ["dom", "esnext"], "experimentalDecorators": true, "emitDecoratorMetadata": true, "module": "esnext", "moduleResolution": "node", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "strict": true, "pretty": true, "removeComments": true, "strictNullChecks": false, "sourceMap": true, "resolveJsonModule": true, "paths": {"@bg-components/*": ["libs/components/src/components/*"], "@bg-configs/*": ["configs/*"], "@bg-services": ["libs/services/src/index.ts"], "@bg-services/*": ["libs/services/src/*"], "@bg-shared": ["libs/shared/src/index.ts"], "@bg-shared/*": ["libs/shared/src/*"], "@bg-webpack": ["workspace/webpack/index.ts"], "@iframe-branding/*": ["apps/iframe-server/src/middlewares/config/branding/*"], "@iframe-shell/*": ["apps/iframe-shell/src/*"], "__mocks__/*": ["libs/shared/__mocks__/*"], "giza/Module": ["apps/iframe-remotes/giza/src/remote-entry.ts"], "roulette/Module": ["apps/iframe-remotes/roulette/src/remote-entry.ts"], "skyward/Module": ["apps/iframe-remotes/skyward/src/remote-entry.ts"], "starzle/Module": ["apps/iframe-remotes/starzle/src/remote-entry.ts"], "styles/*": ["libs/shared/src/styles/*"]}}, "exclude": ["node_modules", "tmp"]}