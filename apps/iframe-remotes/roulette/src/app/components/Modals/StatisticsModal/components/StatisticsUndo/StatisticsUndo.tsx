import { JSX } from 'react';
import classNames from 'classnames';
import {
    GameId,
    t,
    casinoBetAmountsEntity,
    CasinoServiceFactory,
    gamesStatusEntity,
    gamesStatusSelectors,
    casinoBettingSelectors,
} from '@bg-shared';
import { useStore } from '@betgames/bg-state-manager';
import { UndoIcon } from '@bg-components/Icon';
import { ActionButton } from '@bg-components/CasinoChips/Chips';
import classes from './StatisticsUndo.module.scss';

interface IProps {
    gameId: GameId;
    title: string;
}

export const StatisticsUndo = (props: IProps): JSX.Element => {
    const chipsAmounts = useStore(casinoBetAmountsEntity.store(props.gameId));
    const gameStatusStore = gamesStatusEntity.store(props.gameId);
    const openForBets = useStore(gameStatusStore, gamesStatusSelectors.getOpenForBets);
    const chipsService = CasinoServiceFactory.getChips(props.gameId);
    const disabled = !casinoBettingSelectors.hasChips(chipsAmounts) || !openForBets;

    return (
        <div className={classes.container}>
            <div className={classes.text}>
                {t.string('bet_on_statistics_by_tapping').replace('{{X}}', props.title)}
            </div>
            <div>
                <ActionButton
                    disabled={disabled}
                    onClick={() => {
                        chipsService.undoLastAction();
                    }}
                    className={classNames(classes.button, {
                        [classes.disabled]: disabled,
                    })}
                    tooltipMessage="undo"
                >
                    <UndoIcon />
                </ActionButton>
            </div>
        </div>
    );
};
