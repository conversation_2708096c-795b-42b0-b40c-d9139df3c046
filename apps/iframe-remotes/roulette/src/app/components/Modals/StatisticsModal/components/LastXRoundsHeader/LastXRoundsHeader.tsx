import { t } from '@bg-shared';
import { StatisticsSlider } from '@bg-components/Modals/components/StatisticsSlider';
import {
    MAX_STATISTICS_COUNT,
    MIN_STATISTICS_COUNT,
    STATISTICS_INCREMENT,
} from '../../constants/constants';
import classes from './LastXRoundsHeader.module.scss';

interface IProps {
    value: number;
    fixedRange?: boolean;
    setRange(value: number): void;
}

export const LastXRoundsHeader = (props: IProps): JSX.Element => (
    <>
        <div className={classes.infoContainer}>
            <span>{t.string('last_x_rounds').replace('{{count}}', `${MAX_STATISTICS_COUNT}`)}</span>
            {!props.fixedRange && (
                <div className={classes.rangeBox} data-qa="statistics-slider-amount">
                    {props.value}
                </div>
            )}
        </div>
        {!props.fixedRange && (
            <StatisticsSlider
                value={props.value}
                onChange={props.setRange}
                min={MIN_STATISTICS_COUNT}
                max={MAX_STATISTICS_COUNT}
                step={STATISTICS_INCREMENT}
            />
        )}
    </>
);
