import { t } from '@bg-shared';
import { RouletteFrenchCombo } from '../enums/RouletteFrenchCombo';

const tooltipMessages: Record<RouletteFrenchCombo, string> = {
    [RouletteFrenchCombo.ORPHELINS]: 'bet_on_orphelins_tooltip',
    [RouletteFrenchCombo.TIER]: 'bet_on_tier_tooltip',
    [RouletteFrenchCombo.VOISINS]: 'bet_on_voisins_tooltip',
    [RouletteFrenchCombo.ZERO]: 'bet_on_zero_tooltip',
};

export const getBetTooltip = (
    value: number[] | string,
    frenchCombo?: RouletteFrenchCombo,
): string => {
    if (!value.length) {
        return '';
    }

    if (frenchCombo) {
        return t.string(tooltipMessages[frenchCombo] || '');
    }

    if (typeof value === 'string') {
        return t.string('bet_on_x_tooltip').replace('{{X}}', value);
    }

    if (Array.isArray(value) && value.length === 1) {
        return t.string('bet_on_x_tooltip').replace('{{X}}', `${value[0]}`);
    }

    return t
        .string('bet_on_x_and_its_neighbors_tooltip')
        .replace('{{X}}', `${value[Math.floor(value.length / 2)]}`);
};
