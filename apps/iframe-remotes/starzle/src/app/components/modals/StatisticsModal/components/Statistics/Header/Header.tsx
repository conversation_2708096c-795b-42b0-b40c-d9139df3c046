import { JSX } from 'react';
import { t } from '@bg-shared';
import { Row } from '../Row';
import classes from './Header.module.scss';

interface IHeaderProps {
    title: string;
    lastDrawCount?: number;
}

export const Header = (props: IHeaderProps): JSX.Element => (
    <Row className={classes.container} data-qa="area-statistics-header">
        <span className={classes.title}>{props.title}</span>
        {!!props.lastDrawCount && (
            <span className={classes.lastDraw}>
                {t.string('last_x_draws').replace('{{count}}', `${props.lastDrawCount}`)}
            </span>
        )}
    </Row>
);
