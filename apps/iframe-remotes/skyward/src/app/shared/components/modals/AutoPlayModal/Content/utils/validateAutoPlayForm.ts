import { formatAmount } from '@betgames/bg-tools';
import { t, partnerSettingsStore } from '@bg-shared';
import { IAutoPlayForm, ToggleInputsNames } from '../../../../../business/services/BetslipSkyward';

export const validateAutoPlayForm = (values: IAutoPlayForm): string => {
    const { currency } = partnerSettingsStore.partnerSettings;
    const formattedZero = formatAmount(0, currency.precision);
    if (values.numberOfRounds === 0) {
        return t.string('set_number_of_rounds');
    }

    // if only one of decrease and increase stop toggles is ON
    if (
        values[ToggleInputsNames.STOP_INCREASE].enabled !==
        values[ToggleInputsNames.STOP_DECREASE].enabled
    ) {
        return values[ToggleInputsNames.STOP_INCREASE].enabled
            ? t.string('specify_decrease')
            : t.string('specify_increase');
    }

    // if activated toggles (ON) has 0.00 input
    if (
        [
            ToggleInputsNames.STOP_DECREASE,
            ToggleInputsNames.STOP_INCREASE,
            ToggleInputsNames.STOP_EXCEED,
        ].find((fieldName) => values[fieldName].enabled && values[fieldName].value <= 0)
    ) {
        return t.string('can_not_set_zero_stop_point').replace('{{X}}', `${formattedZero}`);
    }

    return '';
};
