import { JSX, useRef } from 'react';
import { useClickOutside } from '@betgames/bg-tools';
import { RippleButton } from '@bg-components/RippleButton';
import { CloseIcon, ChevronIcon } from '@bg-components/Icon';
import { LastResultsAnimated } from '@bg-components/GameContent/LastResultsAnimated';
import { ILastResultModel, IRenderResult, t, MAX_LAST_RESULT, GameId } from '@bg-shared';
import { StatBlock } from '../StatBlock';
import { HotCold } from '../HotCold';
import { ChartBlock } from '../ChartBlock';
import { IChartItem } from '../interfaces';
import { useWheelStatistics } from '../hooks/useWheelStatistics';
import classes from '../WheelStatistics.module.scss';

interface IProps {
    results: ILastResultModel[];
    renderResult: IRenderResult;
    closeStatistics(): void;
}

const LAST_X_DRAWS = '500';

export const FullStatistics = (props: IProps): JSX.Element => {
    const data = useWheelStatistics();
    const node = useRef<HTMLDivElement>(null);

    useClickOutside((target: Node) => {
        if (node.current && !node.current.contains(target)) {
            props.closeStatistics();
        }
    });

    if (!data || !data.distribution) {
        return null;
    }

    const numberOdds: IChartItem[] = [
        { title: t.string('math.odd'), value: data.distribution.odd },
        { title: t.string('math.even'), value: data.distribution.even },
        { title: '1..9', value: data.distribution.from1To9 },
        { title: '10..18', value: data.distribution.from10To18 },
    ];

    const colorOdds: IChartItem[] = [
        { title: t.colors('red'), value: data.distribution.red },
        { title: t.colors('black'), value: data.distribution.black },
        { title: t.colors('grey'), value: data.distribution.grey },
        { title: t.string('cup'), value: data.distribution.white },
    ];

    return (
        <div className={classes.mainContainer} data-qa="area-wheel-statistics" ref={node}>
            <RippleButton
                className={classes.close}
                dataQa="button-wheel-statistics-close"
                onClick={props.closeStatistics}
            >
                <CloseIcon className={classes.icon} />
            </RippleButton>
            <div className={classes.container}>
                <StatBlock
                    title={t.wheelStatistics('last_results')}
                    className={classes.lastResultBlock}
                >
                    <div className={classes.lastResultScrollCase}>
                        <ChevronIcon className={classes.icon} />
                        <LastResultsAnimated
                            resultAmount={MAX_LAST_RESULT[GameId.WHEEL]}
                            results={props.results}
                            firstInvisibility
                            showTime
                            renderResult={props.renderResult}
                            className={classes.lastResultsList}
                        />
                    </div>
                </StatBlock>
                <div className={classes.lastDraws}>
                    <div className={classes.divider}>
                        <div className={classes.title}>
                            <span />
                            {t.string('last_x_draws').replace('{{count}}', LAST_X_DRAWS)}
                        </div>
                    </div>
                    <div className={classes.statBlocksRow}>
                        <StatBlock
                            title={t.wheelStatistics('frequency')}
                            className={classes.hotColdBlock}
                        >
                            <HotCold hot={data.hot} cold={data.cold} />
                        </StatBlock>
                        <StatBlock
                            title={t.wheelStatistics('numbers')}
                            className={classes.numbersBlock}
                        >
                            <ChartBlock charts={numberOdds} />
                        </StatBlock>
                        <StatBlock
                            title={t.wheelStatistics('colors')}
                            className={classes.colorsBlock}
                        >
                            <ChartBlock charts={colorOdds} />
                        </StatBlock>
                    </div>
                </div>
            </div>
        </div>
    );
};
