import { ElementRef, JS<PERSON>, useCallback, useEffect, useRef, useState } from 'react';
import classNames from 'classnames';
import { useStore } from '@betgames/bg-state-manager';
import { LiveInfo, TopNotifications } from '@bg-components/GameContent';
import {
    AutoPick,
    AutoPlay,
    Burger,
    Controls,
    InfoMessage,
    PlayerInfo,
    RGSLoadingScreen,
    Sound,
    Spin,
    StakeSelection,
    Turbo,
    useAutoPlay,
} from '@bg-components/RGS';
import { useHandleError } from '@bg-components/ErrorBoundary';
import { RotateRestriction } from '@bg-components/RotateRestriction';
import {
    balanceSelectors,
    balanceStore,
    betAmountEntity,
    CONTAINER_WIDTH,
    GameId,
    gamesStatusEntity,
    gamesStatusSelectors,
    Logger,
    partnerSettingsStore,
    rgsAutoPlayEntity,
    t,
    useInitialBetAmount,
    useTopNotification,
} from '@bg-shared';
import { EventManager } from '@bg-services';
import { useLandscape, useMobileView } from '@betgames/bg-tools';
import { rgsFreePlayEntity } from '@bg-shared/business/RGSFreePlay';
import { LuckyLennyAssetLoader } from './LuckyLennyExperience/utils/LuckyLennyAssetLoader';
import { mapBoardSymbolIndexToMultiplier } from './LuckyLennyExperience/utils/bonusBoardMappings';
import { gameApi } from './business/services/GameApi/Game.api';
import { gameConfig } from './business/stores/GameConfig.store';
import { gameConfigSelectors } from './business/stores/gameConfig.selectors';
import LennyExperience from './LennyExperience';
import {
    ILuckyLennyBonusGameboardClickEvent,
    ILuckyLennyEventManager,
    ILuckyLennySwitchGameAnimationEvent,
    ILuckyLennySwitchGameEvent,
    ILuckyLennyTurboEvent,
    IPlayResponse,
    IRoundResult,
} from './interfaces';
import { LuckyLennyEvents, LuckyLennyGameId, LuckyLennySound, NextActions } from './enums';
import { luckyLennySoundsService } from './business/services/Sounds';
import { ErrorModal, UnfinishedGameModal } from './components/Modal';
import classes from './LuckyLennyContent.module.scss';

const BALANCE_UPDATE_DELAY = 1000;

const gameId = GameId.LUCKY_LENNY;

export const LuckyLennyContent = (): JSX.Element => {
    const topNotification = useTopNotification();
    const config = useStore(gameConfig);
    const openForBets = useStore(
        gamesStatusEntity.store(gameId),
        gamesStatusSelectors.getOpenForBets,
    );
    const balance = useStore(balanceStore, balanceSelectors.getValue(gameId));
    const betAmount = useStore(betAmountEntity.store(gameId));
    const isMobileView = useMobileView(CONTAINER_WIDTH);
    const isLandscape = useLandscape();
    const eventManagerRef = useRef<ILuckyLennyEventManager>(null);
    const experienceRef = useRef<LennyExperience>(null);

    useInitialBetAmount(gameId, config.initialization.gameSettings.customStakes, true);
    const [isTurbo, setIsTurbo] = useState<boolean>(false);
    const [mounted, setMounted] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(true);
    const [isSwitchGameAnimating, setSwitchGameAnimating] = useState<boolean>(false);
    const [luckyLennyGame, setLuckyLennyGame] = useState<LuckyLennyGameId>(
        LuckyLennyGameId.LennySlotGame,
    );
    const nextActionRef = useRef<NextActions>(NextActions.STANDARD);
    const luckyLennyGameRef = useRef<LuckyLennyGameId>(luckyLennyGame);
    luckyLennyGameRef.current = luckyLennyGame;
    const [showSplashscreen, setShowSplashscreen] = useState<boolean>(true);
    const [showUnfinishedGameModal, setShowUnfinishedGameModal] = useState<boolean>(false);
    const { error, handleError } = useHandleError(gameId);
    const canvasRef = useRef<ElementRef<'canvas'>>(null);
    const isDebugMode = new URLSearchParams(window.location.search).get('debug') === 'true';
    // TODO
    const isMaintenance = false;

    const setOpenForBets = useCallback((value: boolean) => {
        gamesStatusEntity.setStarted(gameId, !value);
        gamesStatusEntity.setOpenForBets(gameId, value);
    }, []);

    const [totalWinAmount, setTotalWinAmount] = useState<number>(null);
    const pendingRespinRef = useRef<boolean>(false);

    // Resize logic will be handled in a dedicated useEffect below

    useEffect(() => {
        setMounted(true);
        eventManagerRef.current = new EventManager<LuckyLennyEvents>();

        // Hook up the global sound service so it can react to game events.
        luckyLennySoundsService.bind(eventManagerRef.current);

        eventManagerRef.current.on(
            // win, reset game
            LuckyLennyEvents.winAnimationEnd,
            () => {
                // enable betting for main game when win animation finishes
                // TODO move to "newGame" event
                if (
                    luckyLennyGameRef.current === LuckyLennyGameId.LennySlotGame ||
                    (luckyLennyGameRef.current === LuckyLennyGameId.LennyWheelGame &&
                        nextActionRef.current === NextActions.WHEEL)
                ) {
                    setOpenForBets(true);
                }
            },
        );
        eventManagerRef.current.on(
            // total win, reset game
            // TODO move to "newGame" event
            LuckyLennyEvents.totalWinAnimationEnd,
            () => {
                setOpenForBets(true);
            },
        );
        eventManagerRef.current.on(
            // no win, reset game
            // TODO make generic event, after "winAnimationEnd" or "totalWinAnimationEnd" events trigger "newGame" event
            LuckyLennyEvents.newGame,
            () => {
                setOpenForBets(true);
            },
        );

        eventManagerRef.current.on<ILuckyLennySwitchGameEvent>(
            LuckyLennyEvents.switchGame,
            ({ gameId: luckyLennyGameId }) => {
                setLuckyLennyGame(luckyLennyGameId);

                if (
                    rgsAutoPlayEntity.store(gameId).value.isActive &&
                    rgsAutoPlayEntity.store(gameId).value.form.stopOnBonusGame &&
                    luckyLennyGameId !== LuckyLennyGameId.LennySlotGame
                ) {
                    rgsAutoPlayEntity.store(gameId).stopAutoPlay();
                }
            },
        );

        eventManagerRef.current.on(LuckyLennyEvents.bonusGameboardAutoPlayStarted, () => {
            setOpenForBets(false);
        });

        eventManagerRef.current.on<ILuckyLennySwitchGameAnimationEvent>(
            LuckyLennyEvents.switchGameAnimation,
            (animating) => {
                setSwitchGameAnimating(animating);
            },
        );

        eventManagerRef.current.on<ILuckyLennySwitchGameEvent>(
            LuckyLennyEvents.switchGame,
            (data) => {
                setTotalWinAmount(data.totalWinAmount);
            },
        );

        return () => {
            eventManagerRef.current = null;
        };
    }, []);

    useEffect(() => {
        if (!mounted || !eventManagerRef.current) {
            return undefined;
        }

        Logger.enable(true, false, 'lenny');
        Logger.log('LuckyLennyGame mounted', 'lenny');
        if (isDebugMode) Logger.log('Debug mode enabled via URL parameter', 'lenny');

        // Set up loading state
        setLoading(true);

        // Initialize game experience
        experienceRef.current = new LennyExperience({
            canvas: canvasRef.current,
            enableDebug: isDebugMode,
            onLoadComplete: () => {
                // Hide the splash screen once loading is complete
                setLoading(false);
            },
            eventManager: eventManagerRef.current,
            width: canvasRef.current.parentElement.offsetWidth,
            height: canvasRef.current.parentElement.offsetHeight,
        });

        return () => {
            if (experienceRef.current) {
                experienceRef.current.dispose();
            }
        };
    }, [mounted, isDebugMode]); // Remove handleResize dependency to prevent remounting

    // --------------------------------------------------------------
    // Unified resize handler – handles both breakpoint changes and
    // browser resize events in a single place.
    // --------------------------------------------------------------
    useEffect(() => {
        let resizeTimerId: number | undefined;

        const handleResize = (): void => {
            if (experienceRef.current && canvasRef.current && canvasRef.current.parentElement) {
                experienceRef.current.handleResize(
                    isMobileView,
                    canvasRef.current.parentElement.offsetWidth,
                    canvasRef.current.parentElement.offsetHeight,
                );
            }
        };

        // Debounce using requestAnimationFrame
        const windowResize = (): void => {
            cancelAnimationFrame(resizeTimerId);
            resizeTimerId = requestAnimationFrame(handleResize);
        };

        window.addEventListener('resize', windowResize);

        // Perform initial sizing
        windowResize();

        return () => {
            cancelAnimationFrame(resizeTimerId);
            window.removeEventListener('resize', windowResize);
        };
    }, [isMobileView]);

    const progressSubscribeHandler = useCallback(
        (callback: (progress: number) => void) => {
            return LuckyLennyAssetLoader.subscribeToProgress((progress) => {
                callback(progress.percentage);
            });
        },
        [loading],
    );

    const handleAutoPlayAmounts = useCallback((response: IPlayResponse, stake: number) => {
        if (rgsAutoPlayEntity.store(gameId).value.isActive) {
            rgsAutoPlayEntity.store(gameId).updateAutoPlayResult(
                // don't add stake (loss) amount on respin or bonus
                response.lastRoundResult.bonusRounds.length ? 0 : stake,
                gameConfigSelectors.getTotalAmountWon(response),
            );
        }
    }, []);

    const handleGameboardTileClick = useCallback((symbolIndex: number) => {
        if (!eventManagerRef.current) {
            return;
        }

        setOpenForBets(false);
        const stake = betAmountEntity.store(gameId).value;

        gameApi
            .playBonusGameboard(
                gameConfig.value.initialization.backendUrl,
                gameConfig.value.initialization.token,
                stake,
                symbolIndex,
            )
            .then((response) => {
                Logger.warn(response, 'lenny');

                // TODO check header error-id
                if (!response.gameState) {
                    const errorString = t.error('transaction_failed_bet_not_accepted');
                    Logger.error(errorString, response);
                    throw Error(errorString);
                }

                nextActionRef.current = response.nextAction;

                // Enabled betting for next gameboard clicking
                if (response.nextAction === NextActions.GAMEBOARD) {
                    setOpenForBets(true);
                }

                experienceRef.current.onBonusGameboardClickResponse(response, symbolIndex);

                handleAutoPlayAmounts(response, stake);
            })
            .catch(handleError);
    }, []);

    const handleSpin = useCallback(() => {
        if (!eventManagerRef.current) {
            return;
        }

        eventManagerRef.current.trigger(LuckyLennySound.SpinButton);
        eventManagerRef.current.trigger(LuckyLennyEvents.bonusWheelSpin);
        eventManagerRef.current.trigger(LuckyLennyEvents.spin);

        setOpenForBets(false);

        if (partnerSettingsStore.isRealtimeBalanceEnabled) {
            // Avoid updating balance with staged bet amount already deducted
            // Staged bet amount should be removed once the bet is resolved
            balanceStore.waitForUpdate(BALANCE_UPDATE_DELAY);
        }

        const stake = betAmountEntity.store(gameId).value;

        gameApi
            .play(
                gameConfig.value.initialization.backendUrl,
                gameConfig.value.initialization.token,
                stake,
            )
            .then((response) => {
                Logger.warn('OnSpinresponse', response, 'lenny');

                // TODO check header error-id
                if (!response.gameState) {
                    const errorString = t.error('transaction_failed_bet_not_accepted');
                    Logger.error(errorString, response, 'lenny');
                    throw Error(errorString);
                }

                nextActionRef.current = response.nextAction;

                rgsFreePlayEntity.store(gameId).update({
                    available: response.freePlaysAvailable,
                    info: response.freePlayInfo,
                });

                experienceRef.current.onSpin(response);

                handleAutoPlayAmounts(response, stake);
            })
            .catch(handleError);
    }, []);

    const handleGameboardAutoPlay = useCallback(() => {
        if (!eventManagerRef.current) {
            return;
        }

        setOpenForBets(false);
        eventManagerRef.current.trigger(LuckyLennyEvents.bonusGameboardAutoPlay);
    }, []);

    const shouldSkipAutoPlay = (): boolean => {
        return (
            pendingRespinRef.current || luckyLennyGameRef.current !== LuckyLennyGameId.LennySlotGame
        );
    };

    useAutoPlay({ gameId, callback: handleSpin, shouldSkipAutoPlay });

    const handleTurbo = (): void => {
        const newTurbo = !isTurbo;
        eventManagerRef.current.trigger<ILuckyLennyTurboEvent>(LuckyLennyEvents.turbo, newTurbo);
        setIsTurbo(newTurbo);
    };

    const handleGameSwitchOnGameStart = useCallback(() => {
        const gameCfg = config.config;

        nextActionRef.current = [
            NextActions.STANDARD,
            NextActions.WHEEL,
            NextActions.GAMEBOARD,
        ].includes(gameCfg.nextAction as NextActions)
            ? (gameCfg.nextAction as NextActions)
            : NextActions.STANDARD;

        switch (gameCfg.nextAction) {
            case NextActions.WHEEL: {
                eventManagerRef.current.trigger<ILuckyLennySwitchGameEvent>(
                    LuckyLennyEvents.switchGame,
                    {
                        gameId: LuckyLennyGameId.LennyWheelGame,
                    },
                );
                break;
            }

            case NextActions.GAMEBOARD: {
                let symbols: number[] = [];

                if (gameCfg.lastRoundResult?.type === 'gameboard') {
                    const roundResCfg = gameCfg.lastRoundResult as IRoundResult;
                    const rawSymbols = roundResCfg?.symbols as number[];
                    const setIndex = roundResCfg?.roundVariables?.setIndex ?? 0;
                    symbols =
                        rawSymbols?.map((idx) => mapBoardSymbolIndexToMultiplier(idx, setIndex)) ??
                        [];

                    Logger.log('LuckyLennyContent: symbols', symbols, 'lenny');
                }

                eventManagerRef.current.trigger<ILuckyLennySwitchGameEvent>(
                    LuckyLennyEvents.switchGame,
                    {
                        gameId: LuckyLennyGameId.LennyAztecGame,
                        symbols,
                    },
                );
                break;
            }

            default:
                // Default to Slot game
                Logger.log('LuckyLennyContent: gameCfg.lastRoundResult.symbols', gameCfg, 'lenny');
                if (gameCfg.lastRoundResult) {
                    Logger.log(
                        'LuckyLennyContent: gameCfg.lastRoundResult.symbols',
                        gameCfg,
                        'lenny',
                    );
                    experienceRef.current.onStartFromScratch(gameCfg.lastRoundResult.symbols);
                    // if slot game && lastRoundResult exists - pending respin action
                    eventManagerRef.current.trigger(LuckyLennyEvents.respin);
                }
                eventManagerRef.current.trigger<ILuckyLennySwitchGameEvent>(
                    LuckyLennyEvents.switchGame,
                    {
                        gameId: LuckyLennyGameId.LennySlotGame,
                    },
                );
                break;
        }
    }, [config]);

    // --------------------------------------------------------------
    // Loading-screen dismissal → start correct game based on
    // `nextAction` that BE might have sent in the *initialisation*
    // payload (open rounds carry-over).
    // if it will be no next action / gameCfg empty, then start slot game
    // --------------------------------------------------------------
    const handleLoadingDismiss = useCallback(() => {
        setShowSplashscreen(false);
        if (!eventManagerRef.current) return;

        const gameCfg = config.config;
        Logger.log('LuckyLennyContent: gameCfg', gameCfg, 'lenny');

        if (gameCfg.lastRoundResult) {
            setShowUnfinishedGameModal(true);
        } else {
            handleGameSwitchOnGameStart();
        }

        experienceRef.current.handleResize(
            isMobileView,
            canvasRef.current.parentElement.offsetWidth,
            canvasRef.current.parentElement.offsetHeight,
        );
    }, [config]);

    const handleUnfinishedGameModalClick = () => {
        setShowUnfinishedGameModal(false);
        handleGameSwitchOnGameStart();
    };

    // Set up additional event handlers after functions are defined
    useEffect(() => {
        if (!eventManagerRef.current) return;

        eventManagerRef.current.on(
            // wheel game - auto spin
            LuckyLennyEvents.bonusWheelAutoSpin,
            () => {
                handleSpin();
            },
        );

        eventManagerRef.current.on<ILuckyLennySwitchGameEvent>(
            LuckyLennyEvents.switchGame,
            (data) => {
                // enable betting when switching to bonus game, for main game there are different events
                if (data.gameId !== LuckyLennyGameId.LennySlotGame) {
                    setOpenForBets(true);
                }
            },
        );

        eventManagerRef.current.on<ILuckyLennyBonusGameboardClickEvent>(
            LuckyLennyEvents.bonusGameboardClick,
            (symbolIndex: number) => {
                handleGameboardTileClick(symbolIndex);
            },
        );

        eventManagerRef.current.on(LuckyLennyEvents.respin, () => {
            pendingRespinRef.current = true;
        });
    }, []);

    useEffect(() => {
        if (pendingRespinRef.current && openForBets) {
            // TODO move to "newGame" or other/new event which indicates that game has ended and call spin
            pendingRespinRef.current = false;
            handleSpin();
        }
    }, [openForBets, handleSpin]);

    const isAutoPlayEnabled = partnerSettingsStore.isAutoPlayEnabled(gameId);
    const isSlotGame = luckyLennyGame === LuckyLennyGameId.LennySlotGame;
    const isBalanceInsufficient = balance < betAmount;
    const isPlaceBetDisabled = !openForBets || (isBalanceInsufficient && isSlotGame);

    return (
        <div
            className={classNames(classes.gameContent, {
                [classes.loaded]: !loading,
                [classes.hideContent]: showSplashscreen,
            })}
        >
            {!!error && <ErrorModal message={error} />}
            {showUnfinishedGameModal && (
                <UnfinishedGameModal onButtonClick={handleUnfinishedGameModalClick} />
            )}
            <RotateRestriction forced={isLandscape} />
            <TopNotifications message={topNotification} className={classes.forceShowContent} />
            {showSplashscreen && (
                <RGSLoadingScreen
                    loading={loading}
                    className={classes.forceShowContent}
                    progressSubscriber={progressSubscribeHandler}
                    onDismiss={handleLoadingDismiss}
                />
            )}
            <div className={classes.container}>
                {mounted && <canvas ref={canvasRef} className={classes.canvas} />}
            </div>
            {!isMaintenance && <LiveInfo gameId={gameId} noLiveIndicator />}

            {!isSwitchGameAnimating && (
                <Controls.Container
                    bonusLayout={!isSlotGame}
                    render={() => {
                        if (isLandscape) {
                            return (
                                <>
                                    <Burger gameId={gameId} />
                                    <PlayerInfo gameId={gameId} />
                                    <InfoMessage
                                        gameId={gameId}
                                        eventManagerRef={eventManagerRef}
                                        totalWinAmount={totalWinAmount}
                                    />
                                    {isSlotGame && (
                                        <StakeSelection
                                            gameId={gameId}
                                            amountsSequence={
                                                config.initialization.gameSettings.customStakes
                                            }
                                            disabled={!openForBets}
                                        />
                                    )}
                                    {luckyLennyGame === LuckyLennyGameId.LennyAztecGame ? (
                                        <AutoPick
                                            onClick={handleGameboardAutoPlay}
                                            disabled={!openForBets}
                                        />
                                    ) : (
                                        <Spin
                                            onClick={handleSpin}
                                            glow={openForBets}
                                            disabled={isPlaceBetDisabled}
                                        />
                                    )}
                                    <Turbo
                                        onClick={handleTurbo}
                                        active={isTurbo}
                                        disabled={!openForBets}
                                        hidden={!isSlotGame}
                                    />
                                    {isAutoPlayEnabled && (
                                        <AutoPlay
                                            disabled={isPlaceBetDisabled}
                                            gameId={gameId}
                                            hidden={!isSlotGame}
                                        />
                                    )}
                                    <Sound />
                                </>
                            );
                        }

                        return (
                            <>
                                <Controls.PortraitTopRow bonusLayout={!isSlotGame}>
                                    <InfoMessage
                                        gameId={gameId}
                                        eventManagerRef={eventManagerRef}
                                        totalWinAmount={totalWinAmount}
                                    />
                                    {isSlotGame && (
                                        <StakeSelection
                                            gameId={gameId}
                                            amountsSequence={
                                                config.initialization.gameSettings.customStakes
                                            }
                                            disabled={!openForBets}
                                        />
                                    )}
                                    {luckyLennyGame === LuckyLennyGameId.LennyAztecGame ? (
                                        <AutoPick
                                            onClick={handleGameboardAutoPlay}
                                            disabled={!openForBets}
                                        />
                                    ) : (
                                        <Spin
                                            onClick={handleSpin}
                                            glow={openForBets}
                                            disabled={isPlaceBetDisabled}
                                        />
                                    )}
                                    {isSlotGame && (
                                        <Turbo
                                            onClick={handleTurbo}
                                            active={isTurbo}
                                            disabled={!openForBets}
                                        />
                                    )}
                                    {isAutoPlayEnabled && isSlotGame && (
                                        <AutoPlay disabled={isPlaceBetDisabled} gameId={gameId} />
                                    )}
                                </Controls.PortraitTopRow>
                                <Controls.PortraitBottomRow>
                                    <Burger gameId={gameId} />
                                    <PlayerInfo gameId={gameId} />
                                    <Sound />
                                </Controls.PortraitBottomRow>
                            </>
                        );
                    }}
                />
            )}
        </div>
    );
};
