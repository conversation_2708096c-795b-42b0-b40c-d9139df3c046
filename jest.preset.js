const nxPreset = require('@nx/jest/preset').default;

module.exports = {
    ...nxPreset,
    globals: {
        _globalConfig: {
            buildType: 'modern',
            environment: 'development',
            apiUrl: '',
            wsUrl: '',
            version: '1.0',
        },
        _featuresConfig: {},
    },
    setupFilesAfterEnv: ['./workspace/setupTests/index.tsx'],
    transformIgnorePatterns: ['node_modules/(?!.pnpm|@rlx/feim)'],
    testPathIgnorePatterns: ['/node_modules/', '/.pnpm-store/', '/.yalc/'],
};
