{"name": "@bg-fe-monorepo/source", "version": "2.0.0", "license": "MIT", "scripts": {"preinstall": "npx only-allow pnpm"}, "private": true, "prettier": "@betgames/prettier-config", "dependencies": {"@amityco/ts-sdk": "6.30.4", "@betgames/bg-state-manager": "4.0.0", "@betgames/bg-tools": "4.0.0", "@esotericsoftware/spine-pixi-v8": "4.2.63", "@juggle/resize-observer": "3.4.0", "@nestjs/common": "11.1.1", "@nestjs/config": "4.0.2", "@nestjs/core": "11.1.0", "@nestjs/platform-express": "11.1.0", "@nestjs/platform-socket.io": "11.1.0", "@nestjs/serve-static": "5.0.3", "@nestjs/websockets": "11.1.0", "@pixi-essentials/gradients": "2.0.0", "@pixi/devtools": "2.0.1", "@pm2/io": "6.1.0", "@rlx/feim": "1.16.0", "@sentry/nestjs": "9.22.0", "@sentry/react": "9.22.0", "@supabase/supabase-js": "2.49.7", "@types/multer": "2.0.0", "betgames-contract-js": "*****************:tvzaidimai/contract-js.git#4.15.0", "bg-player-api": "bitbucket:tvzaidimai/bg-player-api#v2.0.1", "browserslist": "4.22.1", "browserslist-useragent": "4.0.0", "class-transformer": "0.5.1", "class-validator": "0.14.2", "classnames": "2.3.2", "cookie-parser": "1.4.6", "core-js": "3.36.1", "currency.js": "2.0.4", "date-fns": "4.1.0", "detectincognitojs": "1.3.7", "dotenv": "16.3.1", "ejs": "3.1.10", "express": "4.21.1", "gsap": "3.12.5", "howler": "2.2.4", "http-proxy-middleware": "2.0.7", "https": "1.0.0", "immer": "10.1.1", "intersection-observer": "0.12.2", "ioredis": "5.6.1", "lodash": "4.17.21", "lodash-es": "4.17.21", "lodash-unified": "1.0.3", "motion": "10.17.0", "multer": "2.0.1", "normalize-scroll-left": "0.2.1", "normalize.css": "8.0.1", "pixi.js": "8.7.3", "qs": "6.11.2", "react": "18.3.1", "react-datepicker": "8.2.1", "react-dom": "18.3.1", "react-json-view": "1.21.3", "react-paginate": "8.2.0", "react-responsive": "9.0.2", "react-router-dom": "6.20.0", "react-spinners": "0.13.8", "react-tooltip": "5.26.0", "reflect-metadata": "0.2.2", "regenerator-runtime": "0.14.0", "screenfull": "6.0.2", "smoothscroll-polyfill": "0.4.4", "socket.io": "4.7.5", "socket.io-client": "4.7.5", "stats.js": "0.17.0", "three": "^0.171.0", "tslib": "2.3.0", "tweakpane": "4.0.5", "use-context-selector": "1.4.1", "vconsole": "3.15.1", "whatwg-fetch": "3.6.19"}, "devDependencies": {"@babel/core": "7.25.2", "@babel/preset-react": "7.24.7", "@betgames/eslint-config-client": "2.0.0", "@betgames/prettier-config": "3.0.0", "@betgames/stylelint-config": "3.0.0", "@jest/globals": "29.7.0", "@nestjs/cli": "11.0.7", "@nestjs/schematics": "11.0.5", "@nestjs/testing": "11.1.0", "@nx/esbuild": "19.5.1", "@nx/eslint": "19.5.1", "@nx/eslint-plugin": "19.5.1", "@nx/express": "19.5.1", "@nx/jest": "19.5.1", "@nx/js": "19.5.1", "@nx/nest": "21.0.3", "@nx/node": "19.5.1", "@nx/react": "19.5.1", "@nx/rollup": "19.5.1", "@nx/vite": "19.5.1", "@nx/web": "19.5.1", "@nx/webpack": "19.5.1", "@nx/workspace": "19.5.1", "@pmmmwh/react-refresh-webpack-plugin": "0.5.11", "@rollup/plugin-url": "7.0.0", "@sentry/webpack-plugin": "2.10.2", "@svgr/webpack": "8.1.0", "@swc-node/register": "~1.9.2", "@swc/cli": "~0.6.0", "@swc/core": "~1.10.11", "@swc/helpers": "~0.5.15", "@swc/jest": "~0.2.37", "@testing-library/jest-dom": "6.1.4", "@testing-library/react": "15.0.6", "@testing-library/react-hooks": "8.0.1", "@testing-library/user-event": "14.5.1", "@types/browserslist-useragent": "3.0.7", "@types/cookie-parser": "1.4.6", "@types/ejs": "3.1.5", "@types/express": "~4.17.21", "@types/howler": "2.2.11", "@types/ioredis-mock": "8.2.5", "@types/jest": "29.5.10", "@types/lodash-es": "4.17.12", "@types/node": "20.10.1", "@types/qs": "6.9.10", "@types/react": "18.3.1", "@types/react-datepicker": "7.0.0", "@types/react-dom": "18.3.0", "@types/react-paginate": "7.1.4", "@types/react-router-dom": "5.3.3", "@types/rtlcss": "3.5.4", "@types/smoothscroll-polyfill": "0.3.3", "@types/three": "^0.171.0", "@types/ua-parser-js": "0.7.39", "@types/webpack-bundle-analyzer": "4.7.0", "@types/webpack-sources": "3.2.3", "@typescript-eslint/eslint-plugin": "7.7.0", "@typescript-eslint/parser": "7.7.0", "@vitejs/plugin-react-swc": "3.7.1", "@vitest/ui": "1.6.0", "babel-jest": "29.7.0", "babel-loader": "9.1.3", "bundle-stats-webpack-plugin": "4.8.3", "circular-dependency-plugin": "5.2.2", "clean-webpack-plugin": "4.0.0", "compression-webpack-plugin": "10.0.0", "copy-webpack-plugin": "11.0.0", "cross-env": "7.0.3", "css-loader": "6.8.1", "css-minimizer-webpack-plugin": "7.0.0", "cssnano": "6.0.1", "ctix": "2.4.3", "error-overlay-webpack-plugin": "1.1.1", "esbuild": "0.19.2", "eslint": "8.57.0", "eslint-config-prettier": "9.0.0", "eslint-plugin-import": "2.29.0", "eslint-plugin-jest": "27.6.0", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-prettier": "5.0.1", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "html-webpack-plugin": "5.5.3", "ignore-loader": "0.1.2", "image-webpack-loader": "8.1.0", "jest": "29.7.0", "jest-css-modules": "2.1.0", "jest-environment-jsdom": "29.7.0", "jest-environment-node": "29.4.1", "jest-fetch-mock": "3.0.3", "jest-matcher-utils": "29.7.0", "jest-matchmedia-mock": "1.1.0", "jsdom": "~22.1.0", "mini-css-extract-plugin": "2.7.6", "nx": "19.5.1", "nx-stylelint": "17.1.5", "postcss": "8.4.31", "postcss-loader": "7.3.3", "postcss-preset-env": "9.3.0", "postcss-rtlcss": "5.4.0", "prettier": "3.1.1", "process": "0.11.10", "raw-loader": "4.0.2", "react-refresh": "0.14.0", "resolve-url-loader": "5.0.0", "sass": "1.69.5", "sass-loader": "13.3.2", "style-loader": "3.3.3", "stylelint": "15.11.0", "stylelint-config-standard": "34.0.0", "stylelint-config-standard-scss": "11.0.0", "stylelint-declaration-strict-value": "1.9.2", "stylelint-scss": "5.3.2", "stylus": "0.64.0", "supabase": "2.33.7", "swc-loader": "0.2.6", "terser-webpack-plugin": "5.3.9", "ts-jest": "29.1.1", "ts-node": "10.9.1", "typescript": "5.2.2", "url-loader": "4.1.1", "utility-types": "3.10.0", "vite": "5.4.8", "vite-svg-loader": "5.1.0", "vitest": "1.6.0", "webpack": "5.89.0", "webpack-bundle-analyzer": "4.10.1", "webpack-cli": "5.1.4", "webpack-dev-server": "4.15.1", "webpack-merge": "5.10.0", "webpack-sources": "3.2.3", "webpackbar": "5.0.2"}, "pnpm": {"overrides": {"socket.io-client": "4.7.5"}, "onlyBuiltDependencies": ["@betgames/bg-state-manager", "@betgames/bg-tools", "@nestjs/core", "@sentry/cli", "@swc/core", "core-js", "core-js-pure", "cwebp-bin", "esbuild", "gifsicle", "mozjpeg", "nx", "optipng-bin", "pngquant-bin", "supabase"]}, "packageManager": "pnpm@10.14.0"}