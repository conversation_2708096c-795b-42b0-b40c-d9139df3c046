if ENV['APP'].to_s.include?('classic-games-webapp')
  set :deploy_config_path,  File.expand_path('ci-cd/iframe/capistrano/deploy.rb')
  set :stage_config_path,  File.expand_path('ci-cd/iframe/capistrano/stages')
elsif <PERSON>['APP'] == 'iframe-chat-screen'
  set :deploy_config_path,  File.expand_path('ci-cd/chat-iframe/capistrano/deploy.rb')
  set :stage_config_path,  File.expand_path('ci-cd/chat-iframe/capistrano/stages')
else
  raise "Please set the APP environment variable to 'classic-games-webapp' or 'iframe-chat-screen'"
end

require "capistrano/setup"
require "capistrano/deploy"
require 'capistrano/tarball/load'
require 'sshkit'
require 'sshkit/dsl'

include SSHKit::DSL
